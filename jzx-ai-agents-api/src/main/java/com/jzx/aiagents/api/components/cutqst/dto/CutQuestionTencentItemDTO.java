package com.jzx.aiagents.api.components.cutqst.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 腾讯切题接口的题目片段
 */
@Data
public class CutQuestionTencentItemDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -138928728960967L;

    /**
     * 识别的算式是否正确，算式运算结果:
     * ‘YES’:正确
     * ‘NO’: 错误
     * ‘NA’: 非法参数
     * ‘EMPTY’: 未作答
     * 示例值："YES"
     */
    private String item;

    /**
     * 识别出的算式，识别出的文本行字符串
     * 示例值："45.5-37.3=8.2"
     */
    private String itemString;

    /**
     * 识别的算式在图片上的位置信息，文本行在旋转纠正之后的图像中的像素坐 标，表示为(左上角 x, 左上角 y，宽 width， 高 height)
     * 示例值：{ "y": 298, "x": 600, "height": 43, "width": 180 }
     */
    private ItemCoord itemCoord;

    /**
     * 错题推荐答案，算式运算结果正确返回为 ""，算式运算结果错误返回推荐答案 (注:暂不支持多个关系运算符(如 1<10<7)、 无关系运算符(如 frac(1,2)+frac(2,3))、单 位换算(如 1 元=100 角)错题的推荐答案 返回)
     * (注:使用@@标记答案填写区域)
     * 示例值：""
     */
    private String answer;

    /**
     * 算式题型编号，如加减乘除四则题型，具体题型及编号如下：1 加减乘除四则 2 加减乘除已知结果求运算因子3 判断大小 4 约等于估算 5 带余数除法 6 分数四则运算 7 单位换算 8 竖式加减法 9 竖式乘除法 10 脱式计算 11 解方程
     * 注意：此字段可能返回 null，表示取不到有效值。
     * 示例值："1"
     */
    private String expressionType;

    /**
     * 文本行置信度
     * 注意：此字段可能返回 null，表示取不到有效值。
     * 示例值：0
     */
    private Float itemConf;

    /**
     * 用于标识题目 id，如果有若干算式属于同一 题，则其对应的 id 相同。
     * 注意：此字段可能返回 null，表示取不到有效值。
     * 示例值：""
     */
    private String questionId;

    @Data
    public static class ItemCoord {

        /**
         * 坐标高度
         */
        private Long height;

        /**
         * 坐标宽度
         */
        private Long width;

        /**
         * 坐标x
         */
        private Long x;

        /**
         * 坐标y
         */
        private Long y;

    }

}
   
