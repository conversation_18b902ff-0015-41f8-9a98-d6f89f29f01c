package com.jzx.aiagents.api.components.graphickits.req;

import com.jzx.aiagents.api.components.base.req.BaseBizReq;
import java.util.List;
import lombok.Data;

/**
 * gk准入器
 * <AUTHOR>
 * @Date 2025/5/22 21:50
 */
@Data
public class GetGKAccessControllerReq extends BaseBizReq {

    /**
     * gkId
     */
    private String gkId;

    /**
     * 题干+解析+答案
     */
    // private String question;

    /**
     * 题干
     */
    private String term;
    /**
     * 解析
     */
    private String method;
    /**
     * 答案
     */
    private String answer;


    /**
     *  切图
     */
    private String ocrImageUrl;

    /**
     * 路由
     */
    private String route;

    /**
     * 点个数
     */
    private List<String> points;


}
