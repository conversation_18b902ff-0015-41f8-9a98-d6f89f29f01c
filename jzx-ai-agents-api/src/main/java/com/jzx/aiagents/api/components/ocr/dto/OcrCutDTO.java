package com.jzx.aiagents.api.components.ocr.dto;

import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ocr图片裁剪类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OcrCutDTO implements Serializable {

    /**
     * 图片对象
     */
    private byte[] originalImage;

    /**
     * 裁剪的图形base64对象
     */
    private List<String> figureBase64List;

    /**
     * 裁剪的表格base64对象
     */
    private List<String> tableBase64List;

}
