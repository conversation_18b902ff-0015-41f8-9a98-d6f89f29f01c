package com.jzx.aiagents.api.components.ocr.dto;

import com.jzx.aiagents.api.components.base.req.InvokeConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ocr提取请求参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OcrExtractRequest extends InvokeConfig {

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 图片URL
     */
    private String imgUrl;

    /**
     * 是否不需要OCR
     */
    private Boolean ignoreOcr;

}
   
