package com.jzx.aiagents.api.components.ocr.vo;

import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ocr图片提取返回值
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OcrExtractVO implements Serializable {

    /**
     * 识别的题干
     */
    private String stem;

    /**
     * 裁剪的图形base64对象
     */
    private List<String> figureBase64List;

    /**
     * 裁剪的表格base64对象
     */
    private List<String> tableBase64List;

}
