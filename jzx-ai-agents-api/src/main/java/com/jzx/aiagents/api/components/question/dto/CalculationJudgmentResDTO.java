package com.jzx.aiagents.api.components.question.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 计算器判断接口响应参数
 */
@Data
public class CalculationJudgmentResDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -138928728960963104L;

    /**
     * 任务id
     */
    @JsonProperty("task_id")
    private String taskId;
    /**
     * 任务类型
     */
    @JsonProperty("task_type")
    private String taskType;
    /**
     * 状态码
     */
    private String code;

    /**
     * 状态信息
     */
    private String status;

    /**
     * 响应结果
     */
    private RespData data;

    @Data
    public static class RespData {

        /**
         * 计算题分类结果， 1 是口算， 0 是非口算
         */
        private Integer cls;

    }

}
