package com.jzx.aiagents.api.components.question.solution.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 计算题判断接口请求参数
 */
@Data
public class CalculationJudgmentRequest implements Serializable {

    @Serial
    private static final long serialVersionUID = -138928728960963105L;

    /**
     * 任务的唯一 ID
     */
    @JsonProperty("task_id")
    private String taskId;
    /**
     * OSS 文件地址，支持图片下载
     */
    @JsonProperty("img_url")
    private String imgUrl;
    /**
     * 图片的 base64 编码数据（如果未提供 img_url）
     */
    @JsonProperty("img_base64")
    private String imgBase64;
    /**
     * 模型版本 默认使用最新版本
     */
    @JsonProperty("model_version")
    private String modelVersion;

    /**
     * 任务超时时间（默认 10 秒）
     */
    private Integer timeout;
}
