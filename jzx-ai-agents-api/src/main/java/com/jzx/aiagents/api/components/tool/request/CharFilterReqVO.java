package com.jzx.aiagents.api.components.tool.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * <AUTHOR> By AutoGenerator
 */
@Data
public class CharFilterReqVO {

    /**
     * 业务类型，不同的业务可能需要替换的字符串不一样 AI_ANSWER_TEXT_FILTER dify问答返回表情替换 BOARDNOTE_REGEX_FILTER   板书显示正则替换
     */
    @NotBlank(message = "businessType cannot be null or empty")
    private String businessType;


    /**
     * 过滤内容 长度小于5000
     */
    @Size(min = 1, max = 5000, message = "content length must be between 1 and 5000")
    @NotBlank(message = "content cannot be null or empty")
    private String content;


}
