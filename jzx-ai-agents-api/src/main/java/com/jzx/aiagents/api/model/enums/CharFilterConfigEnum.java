package com.jzx.aiagents.api.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2024/6/20 21:40
 */
@Getter
public enum CharFilterConfigEnum {
    /**
     * 板书正则替换
     */
    BOARDNOTE_REGEX_FILTER("BOARDNOTE_REGEX_FILTER", "板书正则替换"),
    /**
     * dify问答表情替换
     */
    AI_ANSWER_TEXT_FILTER("AI_ANSWER_TEXT_FILTER", "dify问答表情替换");

    private String code;
    private String name;

    CharFilterConfigEnum(String status, String message) {
        this.code = status;
        this.name = message;
    }

    /**
     * 判断给定的 code 是否存在于枚举中
     *
     * @param code 要检查的 code 值
     * @return true 如果存在，false 否则
     */
    public static boolean exists(String code) {
        for (CharFilterConfigEnum config : CharFilterConfigEnum.values()) {
            if (config.getCode().equalsIgnoreCase(code)) {
                return true;
            }
        }
        return false;
    }
}
