package com.jzx.aiagents.api.model.enums.graphickits;


import java.util.Objects;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * gk组件场景 apiKey场景枚举
 */
@Getter
public enum ApiKeyGkOtherEnum {
    GK_QUARK_ACCESS_CONTROLLER_QUESTIONSTEM("gkQuarkAccessControllerQuestionstem", "GK准入器-qurk-题干"),
    GK_QUARK_ACCESS_CONTROLLER_QUESTIONMETHODANSWER("gkQuarkAccessControllerQuestionmethodanswer", "GK准入器-qurk_解析+答案"),
    GK_QUARK_ACCESS_CONTROLLER_FILE("gkQuarkAccessControllerFile", "GK准入器-qurk图片"),
    GK_OCR_ACCESS_CONTROLLER_QUESTIONSTEM("gkOcrAccessControllerQuestionstem", "GK准入器-ocr-题干"),
    GK_OCR_ACCESS_CONTROLLER_QUESTIONMETHODANSWER("gkOcrAccessControllerQuestionmethodanswer", "GK准入器-ocr-解析+题干"),
    GK_OCR_ACCESS_CONTROLLER_FILE("gkOcrAccessControllerFile", "GK准入器-ocr-图片"),
    DYNAMIC_SIGNAGE_OBJECT("dynamicSignageObject", "动态标识-对象"),
    DYNAMIC_SIGNAGE_RELATION("dynamicSignageRelation", "动态标识-关系"),
    ;


    private final String type;
    private final String name;

    ApiKeyGkOtherEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public static String getTypeByName(String name) {
        for (ApiKeyGkOtherEnum value : values()) {
            if (Objects.equals(name, value.getName())) {
                return value.type;
            }
        }
        return null;
    }

    public static String getTypeBySubject(String subject, String channel) {
        for (ApiKeyGkOtherEnum value : values()) {
            if (StringUtils.equals(String.format("%s%s", subject, channel), value.getType())) {
                return value.getType();
            }
        }
        return null;
    }
}