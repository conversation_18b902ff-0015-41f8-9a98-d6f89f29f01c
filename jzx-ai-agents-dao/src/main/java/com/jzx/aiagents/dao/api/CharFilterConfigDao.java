package com.jzx.aiagents.dao.api;

import com.jzx.aiagents.dao.model.po.CharFilterConfigPO;
import java.util.List;

/**
 * <AUTHOR> By AutoGenerator
 */
public interface CharFilterConfigDao {

    /**
     * 查询配置的字符串列表
     *
     * @param charType 默认0
     * @return
     */
    List<CharFilterConfigPO> getCharFilterConfigList(String charType);

    boolean batchAddClassStudent(List<CharFilterConfigPO> list);

    boolean deleteCharFilterConfig(Long id);

}