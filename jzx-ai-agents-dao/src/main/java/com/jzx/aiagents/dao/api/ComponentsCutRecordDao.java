package com.jzx.aiagents.dao.api;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jzx.aiagents.dao.model.po.ComponentsCutRecordPO;

/**
 * 组件记录DAO
 */
public interface ComponentsCutRecordDao extends IService<ComponentsCutRecordPO> {

    /**
     * 通过bizId查询组件任务
     *
     * @param bizId
     * @param imageUrlMd5
     * @return
     */
    ComponentsCutRecordPO getByBizId(String bizId, String imageUrlMd5);
}
   