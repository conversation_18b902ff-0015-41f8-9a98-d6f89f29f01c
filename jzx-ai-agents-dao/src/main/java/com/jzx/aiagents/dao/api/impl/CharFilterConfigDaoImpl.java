package com.jzx.aiagents.dao.api.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jzx.aiagents.dao.api.CharFilterConfigDao;
import com.jzx.aiagents.dao.mapper.CharFilterConfigMapper;
import com.jzx.aiagents.dao.model.po.CharFilterConfigPO;
import com.jzx.common.orm.model.values.DeletedStatus;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR> By AutoGenerator
 */
@Repository

public class CharFilterConfigDaoImpl implements CharFilterConfigDao {

    @Autowired
    CharFilterConfigMapper charFilterConfigMapper;

    @Override
    public List<CharFilterConfigPO> getCharFilterConfigList(String businessType) {
        LambdaQueryWrapper<CharFilterConfigPO> queryWrapper = new LambdaQueryWrapper<CharFilterConfigPO>().select(CharFilterConfigPO::getPatternChar, CharFilterConfigPO::getReplaceChar).eq(CharFilterConfigPO::getBusinessType, businessType).eq(CharFilterConfigPO::getDeleted, DeletedStatus.NOT).orderByAsc(CharFilterConfigPO::getId).last("limit 100");
        return charFilterConfigMapper.selectList(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchAddClassStudent(List<CharFilterConfigPO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Boolean.FALSE;
        }
        for (CharFilterConfigPO insert : list) {
            charFilterConfigMapper.insert(insert);
        }
        return Boolean.TRUE;
    }

    @Override
    public boolean deleteCharFilterConfig(Long id) {
        charFilterConfigMapper.deleteById(id);
        return true;
    }
}