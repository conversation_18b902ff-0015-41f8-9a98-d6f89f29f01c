package com.jzx.aiagents.dao.api.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jzx.aiagents.dao.api.ComponentsCutRecordDao;
import com.jzx.aiagents.dao.mapper.ComponentsCutRecordMapper;
import com.jzx.aiagents.dao.model.po.ComponentsCutRecordPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 切题记录DAO实现
 */
@Repository
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ComponentsCutRecordDaoImpl extends ServiceImpl<ComponentsCutRecordMapper, ComponentsCutRecordPO> implements ComponentsCutRecordDao {

    @Override
    public ComponentsCutRecordPO getByBizId(String bizId, String imageUrlMd5) {
        return lambdaQuery().eq(ComponentsCutRecordPO::getBizId, bizId)
            .eq(ComponentsCutRecordPO::getImageUrlMd5, imageUrlMd5)
            .one();
    }

}
   
