package com.jzx.aiagents.dao.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jzx.common.orm.model.entity.BaseDO;
import java.io.Serial;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> By AutoGenerator
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("aa_char_filter_config")
public class CharFilterConfigPO extends BaseDO implements Serializable {

    @Serial
    private static final long serialVersionUID = 6852007391830065528L;

    /**
     * 业务类型，不同的业务可能需要替换的字符串不一样
     */
    @TableField("business_type")
    private String businessType;
    /**
     * 匹配到过滤的具体字符、表情等
     */
    @TableField("pattern_char")
    private String patternChar;

    /**
     * 描述过滤内容的具体含义
     */
    @TableField("description")
    private String description;
    /**
     * 需要替换的字符串或者表情，如果为空则会替换为空
     */
    @TableField("replace_char")
    private String replaceChar;
}
