package com.jzx.aiagents.dao.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jzx.common.orm.model.entity.BaseDO;
import java.io.Serial;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 组件切题记录表
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("aa_components_cut_record")
public class ComponentsCutRecordPO extends BaseDO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 组件任务id
     */
    @TableField("comp_task_id")
    private Long compTaskId;

    /**
     * 业务id（业务请求的唯一标识）
     */
    @TableField("biz_id")
    private String bizId;

    /**
     * 业务编码（业务系统唯一标识）
     */
    @TableField("biz_code")
    private String bizCode;

    /**
     * 切题图片URL
     */
    @TableField("image_url")
    private String imageUrl;

    /**
     * 切题图片URL的md5，用于唯一索引标识
     */
    @TableField("image_url_md5")
    private String imageUrlMd5;

    /**
     * 原始请求参数（JSON格式存储）
     */
    @TableField("request")
    private String request;

    /**
     * 处理结果（JSON格式）
     */
    @TableField("response")
    private String response;

    /**
     * 执行耗时（单位：毫秒）
     */
    @TableField("time_cost")
    private Integer timeCost;

    /**
     * 执行结果：0-失败 1-成功
     */
    @TableField("success")
    private Integer success;

    /**
     * 错误信息（最多1000字符）
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 供应商编码（关联供应商配置）
     */
    @TableField("provider_code")
    private String providerCode;
}
   
