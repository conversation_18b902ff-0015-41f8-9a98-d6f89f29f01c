package com.jzx.aiagents.service.components.conversation.tts.impl;

import com.jzx.aiagents.api.model.enums.TtsSubjectEnums;
import com.jzx.aiagents.dao.model.po.TtsStringConfigDO;
import com.jzx.aiagents.manager.enums.TextClassifyType;
import com.jzx.aiagents.manager.enums.TtsConfigTypeEnums;
import com.jzx.aiagents.service.components.conversation.tts.api.TtsCharConfigService;
import com.jzx.aiagents.service.components.conversation.tts.api.TtsStringConfigService;
import com.jzx.aiagents.service.components.conversation.tts.dto.LatexText;
import com.jzx.aiagents.service.components.conversation.tts.dto.TextClassify;
import com.jzx.aiagents.service.components.conversation.tts.utils.FormulaFixUtil;
import com.jzx.aiagents.service.components.conversation.tts.utils.LatexUtil;
import com.jzx.aiagents.service.components.conversation.tts.utils.VoiceUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Service("physicsSubjectTextProcessService")
@Slf4j
public class PhysicsSubjectTextProcessServiceImpl extends DefaultSubjectTextProcessServiceImpl {


    public PhysicsSubjectTextProcessServiceImpl(TtsCharConfigService ttsCharConfigService, TtsStringConfigService ttsStringConfigService) {
        super(ttsCharConfigService, ttsStringConfigService);
    }

    @Override
    public String getCode() {
        return TtsSubjectEnums.PHYSICS.code();
    }

    @Override
    public String doProcessSsmlContent(String providerCode, String text) {
        //全角替换成半角
        text = VoiceUtil.processSpecialChar(ttsCharConfigService.getTtsCharConfigList(providerCode, TtsSubjectEnums.DEFAULT.code(), TtsConfigTypeEnums.BEFORE.getCode()), text);
//        text = VoiceUtil.processStringReplace(text, ttsStringConfigService.getTtsStringConfigList(providerCode, getCode(), TtsConfigTypeEnums.OLD_LATEX.getCode()));
        text = processTempPolyphone(text);

        text = FormulaFixUtil.formulaReplace(text, ttsStringConfigService.getTtsStringConfigList(providerCode, getCode(),TtsConfigTypeEnums.FORMULA.getCode()));
        text = VoiceUtil.processStringReplace(text, ttsStringConfigService.getTtsStringConfigList(providerCode, TtsSubjectEnums.DEFAULT.code(), TtsConfigTypeEnums.SPEED_UNIT.getCode()));
        text = VoiceUtil.processStringReplace(text, ttsStringConfigService.getTtsStringConfigList(providerCode, TtsSubjectEnums.DEFAULT.code(), TtsConfigTypeEnums.MATH_UNIT.getCode()));

        text = VoiceUtil.processStringReplace(text, ttsStringConfigService.getTtsStringConfigList(providerCode, TtsSubjectEnums.DEFAULT.code(), TtsConfigTypeEnums.MATH_COORDINATE.getCode()));


        boolean containsRatio = false;
        List<TtsStringConfigDO> ttsStringConfigList = ttsStringConfigService.getTtsStringConfigList(providerCode, TtsSubjectEnums.DEFAULT.code(), TtsConfigTypeEnums.RATIO_KEYWORD.getCode());
        for (TtsStringConfigDO ttsStringConfigDO : ttsStringConfigList) {
            if (StringUtils.contains(text, ttsStringConfigDO.getStrValue())) {
                containsRatio = true;
                break;
            }
        }
        List<TextClassify> classifyList = VoiceUtil.extractMathText(text,
                ttsCharConfigService.getTtsCharConfigList(providerCode, getCode(), TtsConfigTypeEnums.MATH_VAR_ELEMENT.getCode()),
                ttsStringConfigService.getTtsStringConfigList(providerCode, TtsSubjectEnums.DEFAULT.code(), TtsConfigTypeEnums.OPTION_BEFORE_COLON.getCode()),
                ttsStringConfigService.getTtsStringConfigList(providerCode, getCode(), TtsConfigTypeEnums.UNIT_WORD.getCode()),
                ttsStringConfigService.getTtsStringConfigList(providerCode, getCode(), TtsConfigTypeEnums.DELTA_TRI_KEY.getCode()),
                containsRatio);
        StringBuilder sb = new StringBuilder();
        for (TextClassify textClassify : classifyList) {
            String innerText = textClassify.getText();
            if (textClassify.getType() == TextClassifyType.EN_TEXT) {
                sb.append(innerText);
            } else {
                sb.append(processChildSsmlContent(providerCode, innerText, getCode()));
            }
        }
        text = sb.toString();
        text = VoiceUtil.processSpecialChar(ttsCharConfigService.getTtsCharConfigList(providerCode, getCode(), TtsConfigTypeEnums.AFTER.getCode()), text);
        return removeBlankKH(text);
    }

    @Override
    protected String processEnglish(String text, String subject) {
        // 处理三角函数和各种英文读音
        String regex = String.join("|", VoiceUtil.mathFunctions.keySet());
        Matcher matcher1 = Pattern.compile(regex).matcher(text);
        StringBuffer sb1 = new StringBuffer();
        while (matcher1.find()) {
            String matchedKey = matcher1.group();
            matcher1.appendReplacement(sb1, VoiceUtil.mathFunctions.get(matchedKey));
        }
        matcher1.appendTail(sb1);
        text = sb1.toString();

        // 处理三角函数以外的英文
        Matcher matcher = VoiceUtil.FUNCTION_ENG.matcher(text);
        StringBuilder sb = new StringBuilder();
        while (matcher.find()) {
            String word = matcher.group(2);
            if (StringUtils.isNotBlank(word) && !VoiceUtil.mathFunctions.containsKey(word)){
                matcher.appendReplacement(sb, "<say-as interpret-as='characters'>" + word + "</say-as>");
            }
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    @Override
    public String processSsmlContent(String providerCode, String text) {
        StringBuilder sb = new StringBuilder();
        List<LatexText> latexTexts;
        try {
            text = VoiceUtil.processStringReplace(text, ttsStringConfigService.getTtsStringConfigList(providerCode, getCode(),TtsConfigTypeEnums.UNIT_WORD.getCode()));
            latexTexts = LatexUtil.extractLatexTexts(text);
        } catch (Exception e) {
            log.error("Physics.DefaultSubjectTextProcessServiceImpl extract latex error. {}", text, e);
            latexTexts = List.of(new LatexText(text, false));
        }
        for (LatexText latexText : latexTexts) {
            if (latexText.isLatex()) {
                sb.append(latexText.getText());
            } else {
                sb.append(doProcessSsmlContent(providerCode, latexText.getText()));
            }
        }
        return sb.toString();
    }
}
