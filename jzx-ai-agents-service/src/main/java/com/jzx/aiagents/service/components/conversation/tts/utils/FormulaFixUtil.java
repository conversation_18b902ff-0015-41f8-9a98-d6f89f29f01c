package com.jzx.aiagents.service.components.conversation.tts.utils;

import com.jzx.aiagents.dao.model.po.TtsStringConfigDO;

import java.util.*;
import java.util.regex.*;

public class FormulaFixUtil {
    private static final Map<String, String> FORMULA_MAP = new LinkedHashMap<>() {{
        put("v = s / t", "速度=路程除以时间");
        put("F = ma", "力=质量乘以加速度");
        put("p = ρgh", "液体压强 = 密度 × 重力加速度 × 深度");
        put("P = W / t = Fv", "功率 = 功 ÷ 时间 = 力 × 速度");
        put("p = F / S", "压强 = 压力 ÷ 受力面积");
    }};

    private static final List<Map.Entry<Pattern, String>> RULES = new ArrayList<>();

    static {
        FORMULA_MAP.forEach((formula, trans) -> {
            String regex = buildRegex(formula);
            System.out.println("Generated regex for " + formula + ": " + regex); // 调试输出
            RULES.add(new AbstractMap.SimpleEntry<>(
                    Pattern.compile(regex, Pattern.CASE_INSENSITIVE),
                    trans
            ));
        });

        RULES.sort((a, b) ->
                Integer.compare(b.getKey().pattern().length(), a.getKey().pattern().length())
        );
    }

    private static String buildRegex(String formula) {
        StringBuilder sb = new StringBuilder();
        formula = formula.replaceAll("\\s+", " ").trim();

        for (char c : formula.toCharArray()) {
            if (Character.isWhitespace(c)) {
                sb.append("\\s*+");
            } else {
                // 转义正则特殊字符，不包括斜杠
                if ("+*?()[]{}^$.|\\".indexOf(c) != -1) {
                    sb.append("\\");
                }
                sb.append(c);
            }
        }
        return sb.toString();
    }

    public static String translate(String text) {
        StringBuffer result = new StringBuffer(text);

        for (Map.Entry<Pattern, String> entry : RULES) {
            Matcher matcher = entry.getKey().matcher(result);
            StringBuffer temp = new StringBuffer();

            while (matcher.find()) {
                String matched = matcher.group();
                String cleanMatched = matched.replaceAll("\\s+", "").toLowerCase();
                boolean isValid = FORMULA_MAP.keySet().stream()
                        .anyMatch(k -> k.replaceAll("\\s+", "").equalsIgnoreCase(cleanMatched));

                if (isValid) {
                    matcher.appendReplacement(temp, Matcher.quoteReplacement(entry.getValue()));
                } else {
                    matcher.appendReplacement(temp, Matcher.quoteReplacement(matched));
                }
            }
            matcher.appendTail(temp);
            result = temp;
        }
        return result.toString();
    }

    public static String formulaReplace(String text, List<TtsStringConfigDO> ttsStringConfigList) {
        if (text == null || text.isEmpty()) return text;
        if (ttsStringConfigList == null || ttsStringConfigList.isEmpty()) return text;

        // 构建动态的公式映射
        Map<String, String> dynamicFormulaMap = new LinkedHashMap<>();
        for (TtsStringConfigDO config : ttsStringConfigList) {
            dynamicFormulaMap.put(config.getStrValue(), config.getNewValue());
        }

        // 生成正则规则列表
        List<Map.Entry<Pattern, String>> dynamicRules = new ArrayList<>();
        dynamicFormulaMap.forEach((formula, trans) -> {
            String regex = buildRegex(formula);
            dynamicRules.add(new AbstractMap.SimpleEntry<>(
                    Pattern.compile(regex, Pattern.CASE_INSENSITIVE),
                    trans
            ));
        });

        // 按正则表达式长度降序排序
        dynamicRules.sort((a, b) ->
                Integer.compare(b.getKey().pattern().length(), a.getKey().pattern().length())
        );

        StringBuffer result = new StringBuffer(text);

        for (Map.Entry<Pattern, String> entry : dynamicRules) {
            Matcher matcher = entry.getKey().matcher(result);
            StringBuffer temp = new StringBuffer();

            while (matcher.find()) {
                String matched = matcher.group();
                String cleanMatched = matched.replaceAll("\\s+", "").toLowerCase();
                // 检查是否匹配动态配置中的公式（去除空格后一致）
                boolean isValid = dynamicFormulaMap.keySet().stream()
                        .anyMatch(k -> k.replaceAll("\\s+", "").equalsIgnoreCase(cleanMatched));

                if (isValid) {
                    matcher.appendReplacement(temp, Matcher.quoteReplacement(entry.getValue()));
                } else {
                    matcher.appendReplacement(temp, Matcher.quoteReplacement(matched));
                }
            }
            matcher.appendTail(temp);
            result = temp;
        }

        return result.toString();
    }

    public static void main(String[] args) {
        String input = "今天我们V S要学习p = F/ S的公式是V = S / t，牛顿定律F = ma,液体压强p=ρgh";
        System.out.println("输入：" + input);
        List<TtsStringConfigDO> list = new ArrayList<>();
//        TtsStringConfigDO d1 = new TtsStringConfigDO();
//        d1.setStrValue("p=F/S");
//        d1.setNewValue("压强 = 压力 ÷ 受力面积");
//        list.add(d1);
//        System.out.println("输出：" + formulaReplace(input,list));
        System.out.println("输出：" + translate(input));
    }
}