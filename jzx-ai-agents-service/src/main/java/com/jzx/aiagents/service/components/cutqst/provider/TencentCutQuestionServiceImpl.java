package com.jzx.aiagents.service.components.cutqst.provider;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.jzx.aiagents.api.components.base.dto.BizImageDTO;
import com.jzx.aiagents.api.components.base.dto.PointDTO;
import com.jzx.aiagents.api.components.cutqst.dto.CutQuestionResDTO;
import com.jzx.aiagents.api.components.cutqst.dto.CutQuestionSliceDTO;
import com.jzx.aiagents.api.components.cutqst.dto.CutQuestionTencentItemDTO;
import com.jzx.aiagents.api.model.enums.ComponentCodeEnum;
import com.jzx.aiagents.api.model.enums.ProviderEnum;
import com.jzx.aiagents.dao.api.ComponentsCutRecordDao;
import com.jzx.aiagents.dao.model.po.ComponentsCutRecordPO;
import com.jzx.aiagents.dao.util.ProviderCallback;
import com.jzx.aiagents.service.components.cutqst.api.CutQuestionService;
import com.tencentcloudapi.common.AbstractModel;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.hcm.v20181106.HcmClient;
import com.tencentcloudapi.hcm.v20181106.models.EvaluationRequest;
import com.tencentcloudapi.hcm.v20181106.models.EvaluationResponse;
import com.tencentcloudapi.hcm.v20181106.models.Item;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 腾讯计算题切题服务实现
 */
@Service("tencentCutQuestionService")
@RequiredArgsConstructor
@Slf4j
@RefreshScope
public class TencentCutQuestionServiceImpl implements CutQuestionService {

    @Value("${jzx.components.cut-question.provider.tencent.endpoint:hcm.ap-shanghai-fsi.tencentcloudapi.com}")
    private String tencentCutQuestionEndpoint;

    @Value("${jzx.components.cut-question.provider.tencent.secretId:AKIDizP6FNSeq6NbpyDZwKnG0qiZTQSTHEAA}")
    private String secretId;

    @Value("${jzx.components.cut-question.provider.tencent.secretKey:RqGlt2IX2YB65CSqO0u3rjlL6lItQ4jU}")
    private String secretKey;

    @Value("${jzx.components.cut-question.provider.tencent.overlapRate:0.5}")
    private double overlapAreaRate;

    @Value("${jzx.components.cut-question.provider.tencent.rowTolerance:20}")
    private int rowTolerance;

    @Autowired
    private ComponentsCutRecordDao componentsCutRecordDao;

    @Override
    public String getCode() {
        return ProviderEnum.TENCENT.getCode();
    }

    @Override
    public List<CutQuestionResDTO> cutQuestion(long compTaskId, List<BizImageDTO> bizImgList, ProviderCallback callback) {
        // 创建响应参数
        List<CutQuestionResDTO> result = new ArrayList<>();
        for (BizImageDTO bizImageDTO : bizImgList) {
            long start = System.currentTimeMillis();
            if (CollectionUtils.isEmpty(bizImageDTO.getSlices())) {
                // 腾讯切题场景，如果自研切题的中框结果为空，则跳过该图片，因为腾讯小框结果无法关联到自研中框
                log.warn("自研切题中框结果为空，跳过该图片，imgUrl: {}", bizImageDTO.getImgUrl());
                continue;
            }
            String requestBody = bizImageDTO.getImgUrl();
            try {
                String body = null;
                EvaluationResponse response;
                String imageUrlMd5 = DigestUtils.md5Hex(bizImageDTO.getImgUrl());
                ComponentsCutRecordPO cutRecordPO = componentsCutRecordDao.getByBizId(bizImageDTO.getBizId(), imageUrlMd5);
                if (cutRecordPO != null) {
                    body = cutRecordPO.getResponse();
                }
                if (StringUtils.hasText(body)) {
                    log.info("invoke cut tencent question method from db final result: {}", body);
                    response = EvaluationResponse.fromJsonString(body, EvaluationResponse.class);
                } else {
                    try {
                        response = getTencentCutResult(requestBody);
                    } catch (Exception e) {
                        log.error("腾讯切题请求异常：{}", e.getMessage());
                        if (callback != null) {
                            callback.setError("tencentCutQuestion", e, requestBody, e.getMessage());
                        }
                        saveCutRecord(compTaskId, bizImageDTO.getBizId(), bizImageDTO.getImgUrl(), imageUrlMd5, requestBody, body, 0, (int) (System.currentTimeMillis() - start), e.getMessage());
                        throw new RuntimeException(e);
                    }
                    body = JSON.toJSONString(response);
                    log.info("invoke cut tencent question method final result: {}", body);
                    if (callback != null) {
                        callback.setResult("tencentCutQuestion", result, requestBody, body);
                    }
                    // 保存切图结果
                    saveCutRecord(compTaskId, bizImageDTO.getBizId(), bizImageDTO.getImgUrl(), imageUrlMd5, requestBody, body, 1, (int) (System.currentTimeMillis() - start), null);
                }
                if (response == null || response.getItems() == null || response.getItems().length == 0) {
                    long end = System.currentTimeMillis();
                    log.warn("腾讯切题失败耗时:{}ms, errMsg:{}", end - start, body);
                    CutQuestionResDTO cutQuestionResDTO = new CutQuestionResDTO();
                    cutQuestionResDTO.setSuccess(false);
                    cutQuestionResDTO.setErrorMessage("腾讯切题失败");
                    cutQuestionResDTO.setBizId(bizImageDTO.getBizId());
                    cutQuestionResDTO.setImgUrl(bizImageDTO.getImgUrl());
                    cutQuestionResDTO.setImgUrlCorrection(bizImageDTO.getImgUrl());
                    result.add(cutQuestionResDTO);
                    continue;
                }
                CutQuestionResDTO cutQuestionResDTO = new CutQuestionResDTO();
                cutQuestionResDTO.setBizId(bizImageDTO.getBizId());
                cutQuestionResDTO.setImgUrl(bizImageDTO.getImgUrl());
                cutQuestionResDTO.setImgUrlCorrection(bizImageDTO.getImgUrl());
                cutQuestionResDTO.setSuccess(true);
                List<CutQuestionSliceDTO> sliceList = new ArrayList<>();
                // 遍历大的切题框，将腾讯的小切题框和大切题框进行面积重合度比对，然后放入同一个切题框中
                for (CutQuestionSliceDTO slice : bizImageDTO.getSlices()) {
                    CutQuestionSliceDTO sliceDTO = new CutQuestionSliceDTO();
                    sliceDTO.setQuestionSliceId(slice.getQuestionSliceId());
                    sliceDTO.setPostion(slice.getPostion());
                    sliceDTO.setItems(getOverlapAreaItems(response.getItems(), slice.getPostion()));
                    sliceList.add(sliceDTO);
                }
                cutQuestionResDTO.setSlices(sliceList);
                result.add(cutQuestionResDTO);
            } catch (Exception e) {
                log.error("腾讯切题请求异常：{}", e.getMessage());
                if (callback != null) {
                    callback.setError("tencentCutQuestion", e, requestBody, e.getMessage());
                }
                throw new RuntimeException(e);
            }
        }
        return result;
    }

    private List<CutQuestionTencentItemDTO> getOverlapAreaItems(Item[] items, List<PointDTO> postion) {
        List<CutQuestionTencentItemDTO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(postion)) {
            log.warn("自研切题中框坐标点为空，跳过该坐标点， position: {}", JSON.toJSONString(postion));
            return result;
        }
        // 计算 postion 的边界框
        int minX = Integer.MAX_VALUE;
        int minY = Integer.MAX_VALUE;
        int maxX = Integer.MIN_VALUE;
        int maxY = Integer.MIN_VALUE;

        for (PointDTO point : postion) {
            minX = Math.min(minX, point.getX());
            minY = Math.min(minY, point.getY());
            maxX = Math.max(maxX, point.getX());
            maxY = Math.max(maxY, point.getY());
        }
        for (Item item : items) {
            // 计算 Item 的边界框
            Long itemX = item.getItemCoord().getX();
            Long itemY = item.getItemCoord().getY();
            Long itemWidth = item.getItemCoord().getWidth();
            Long itemHeight = item.getItemCoord().getHeight();
            long itemMaxX = itemX + itemWidth;
            long itemMaxY = itemY + itemHeight;

            // 计算重合区域
            long overlapMinX = Math.max(minX, itemX);
            long overlapMinY = Math.max(minY, itemY);
            long overlapMaxX = Math.min(maxX, itemMaxX);
            long overlapMaxY = Math.min(maxY, itemMaxY);

            long overlapWidth = Math.max(0, overlapMaxX - overlapMinX);
            long overlapHeight = Math.max(0, overlapMaxY - overlapMinY);
            long overlapArea = overlapWidth * overlapHeight;
            long itemArea = itemWidth * itemHeight;
            // 计算重合度
            double overlapRatio = (double) overlapArea / itemArea;

            if (overlapRatio > overlapAreaRate) {
                result.add(copyItem(item));
            }
        }
        return sortItemsByRowAndX(result);
    }

    private List<CutQuestionTencentItemDTO> sortItemsByRowAndX(List<CutQuestionTencentItemDTO> items) {
        if (items == null || items.isEmpty()) {
            return Collections.emptyList();
        }

        // 1: 将元素按 y 分组（考虑容差）
        Map<Long, List<CutQuestionTencentItemDTO>> rowMap = new LinkedHashMap<>();
        items.forEach(item -> {
            Long y = item.getItemCoord().getY() + item.getItemCoord().getHeight();
            // 寻找已有的近似行
            Long key = rowMap.keySet().stream()
                .filter(k -> Math.abs(k - y) <= rowTolerance)
                .findFirst()
                .orElse(y);
            rowMap.computeIfAbsent(key, k -> new ArrayList<>()).add(item);
        });

        // 2: 对每一行内部按 x 排序，并合并结果
        return rowMap.values().stream()
            .map(list -> list.stream()
                .sorted(Comparator.comparingLong(i -> i.getItemCoord().getX() + i.getItemCoord().getWidth()))
                .collect(Collectors.toList()))
            .flatMap(List::stream)
            .collect(Collectors.toList());
    }

    private EvaluationResponse getTencentCutResult(String imageUrl) throws TencentCloudSDKException {
        try {
            long start = System.currentTimeMillis();
            log.info("cutQuestion 腾讯切题开始...:{}", imageUrl);
            Credential cred = new Credential(secretId, secretKey);
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint(tencentCutQuestionEndpoint);
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            HcmClient client = new HcmClient(cred, "", clientProfile);
            // 实例化一个请求对象,每个接口都会对应一个request对象
            EvaluationRequest req = new EvaluationRequest();
            req.setSessionId(IdUtil.fastUUID());
            req.setUrl(imageUrl);
            // 返回的resp是一个EvaluationResponse的实例，与请求对象对应
            EvaluationResponse resp = client.Evaluation(req);
            long end = System.currentTimeMillis();
            // 输出json格式的字符串回包
            log.info("cutQuestion 腾讯切题结束：cost:{}ms, url:{}, res:{}", end - start, imageUrl,
                AbstractModel.toJsonString(resp));
            return resp;
        } catch (Exception e) {
            log.error("cutQuestion 腾讯切题异常：", e);
            throw e;
        }
    }

    private CutQuestionTencentItemDTO copyItem(Item item) {
        CutQuestionTencentItemDTO itemDTO = new CutQuestionTencentItemDTO();
        if (item == null) {
            return itemDTO;
        }
        itemDTO.setItem(item.getItem());
        itemDTO.setItemString(item.getItemString());
        itemDTO.setAnswer(itemDTO.getAnswer());
        itemDTO.setExpressionType(item.getExpressionType());
        itemDTO.setItemConf(item.getItemConf());
        itemDTO.setQuestionId(itemDTO.getQuestionId());
        CutQuestionTencentItemDTO.ItemCoord itemCoord = new CutQuestionTencentItemDTO.ItemCoord();
        itemCoord.setHeight(item.getItemCoord().getHeight());
        itemCoord.setWidth(item.getItemCoord().getWidth());
        itemCoord.setX(item.getItemCoord().getX());
        itemCoord.setY(item.getItemCoord().getY());
        itemDTO.setItemCoord(itemCoord);
        return itemDTO;
    }

    private void saveCutRecord(long compTaskId, String bizId, String imageUrl, String imageUrlMd5, String request, String response, Integer success, int timeCost, String errorMessage) {
        ComponentsCutRecordPO cutRecordPO = new ComponentsCutRecordPO();
        cutRecordPO.setCompTaskId(compTaskId)
            .setBizId(bizId)
            .setBizCode(ComponentCodeEnum.CUT_QUESTION.getCode())
            .setImageUrl(imageUrl)
            .setImageUrlMd5(imageUrlMd5)
            .setRequest(request)
            .setResponse(response)
            .setTimeCost(timeCost)
            .setSuccess(success)
            .setErrorMessage(errorMessage)
            .setProviderCode(getCode());
        componentsCutRecordDao.save(cutRecordPO);
    }

}
   
