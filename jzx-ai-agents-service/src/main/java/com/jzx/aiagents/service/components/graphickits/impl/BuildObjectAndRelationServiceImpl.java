package com.jzx.aiagents.service.components.graphickits.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.jzx.aiagents.api.model.enums.graphickits.ApiKeyGkSceneEnum;
import com.jzx.aiagents.service.components.graphickits.BuildObjectAndRelationService;
import com.jzx.aiagents.service.components.graphickits.dto.BuildObjectsAndRelationsContext;
import com.jzx.aiagents.service.components.graphickits.dto.PointDTO;
import com.jzx.aiagents.service.components.graphickits.dto.PointSparnll;
import com.jzx.aiagents.service.components.graphickits.util.LetterExtractor;
import com.jzx.aiagents.service.components.graphickits.util.ParabolaSolver;
import com.jzx.aiagents.service.components.graphickits.util.SymmetricPointCalculator;
import com.jzx.aiagents.service.components.tools.JsonFixedService;
import com.jzx.aiagents.service.components.tools.LatexFixedService;
import com.jzx.common.lang.annotation.AutoLog;
import com.jzx.common.lang.exception.BizException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.mariuszgromada.math.mxparser.Argument;
import org.mariuszgromada.math.mxparser.Expression;
import org.mariuszgromada.math.mxparser.mXparser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 对象转换
 *
 * <AUTHOR>
 * @date 2025/2/11 10:20:38
 */
@Component
@Slf4j
public class BuildObjectAndRelationServiceImpl implements BuildObjectAndRelationService {

    @Autowired
    private JsonFixedService jsonFixedService;
    @Autowired
    private LatexFixedService latexFixedService;

    /**
     * 组装对象＆关系
     *
     * @param buildObjectsAndRelationsContext
     * @return
     */
    @AutoLog
    @Override
    public Map<String, List<Map>> buildObjectsAndRelations(
        BuildObjectsAndRelationsContext buildObjectsAndRelationsContext) {

        for (String s : buildObjectsAndRelationsContext.getRsponseMap().keySet()) {
            // 处理反引号
            buildObjectsAndRelationsContext.getRsponseMap()
                .put(s, buildObjectsAndRelationsContext.getRsponseMap().get(s)
                    .replaceAll("`", "\'")
                    .replaceAll("′", "\'"));
        }

        // 提取点对象
        Map<String, Map> pointObjects = null;
        try {
            pointObjects = extractPointObjects(buildObjectsAndRelationsContext.getIdCount(),
                buildObjectsAndRelationsContext.getRsponseMap());
        } catch (Exception e) {
            log.error("提点对象失败 gkId {}", buildObjectsAndRelationsContext.getGkId(), e);
            throw new BizException("提点对象失败" + " " + buildObjectsAndRelationsContext.getGkId());
        }
        Map<String, Map> pointObjectsRaw = new HashMap<>(pointObjects);
        Map<String, Map> pointObjectsTmp = new HashMap<>();
        boolean lostPoint = false;
        // 过滤出和测绘点的交集
        Map<String, PointDTO> ptm = buildObjectsAndRelationsContext.getInaccuratePoints().stream()
            .collect(Collectors.toMap(PointDTO::getName, v -> v, (k1, k2) -> k2));
        for (String s : pointObjects.keySet()) {
            if (ptm.containsKey(s)) {
                pointObjectsTmp.put(s, pointObjects.get(s));
            } else {
                if (pointObjects.get(s).get("qx") != null
                    && pointObjects.get(s).get("qy") != null) {
                    // 如果有点坐标可以通过计算获取测绘先保留点
                    pointObjects.get(s).put("needCalculate", 1);
                    pointObjectsTmp.put(s, pointObjects.get(s));
                } else {

                    List s4 = JSONObject.parseObject(
                        buildObjectsAndRelationsContext.getRsponseMap().get(ApiKeyGkSceneEnum.CIRCULAR_ARC.getType()),
                        List.class);
                    if (s4 == null) {
                        s4 = new ArrayList();
                    }
                    for (int i = 0; i < s4.size(); i++) {
                        List l = (List) s4.get(i);
                        // 圆弧的圆心名称
                        String o = (String) l.get(0);
                        if (l.size() >= 4 && Objects.equals(pointObjects.get(s).get("name"), o)) {
                            pointObjectsTmp.put(s, pointObjects.get(s));
                            break;
                        }
                    }
                    s4 = JSONObject.parseObject(
                        buildObjectsAndRelationsContext.getRsponseMap().get(ApiKeyGkSceneEnum.COMPASS_ARC.getType()),
                        List.class);
                    if (s4 == null) {
                        s4 = new ArrayList();
                    }
                    for (int i = 0; i < s4.size(); i++) {
                        List l = (List) s4.get(i);
                        // 圆规弧的圆心名称
                        String o = (String) l.get(0);
                        if (l.size() >= 4 && Objects.equals(pointObjects.get(s).get("name"), o)) {
                            pointObjectsTmp.put(s, pointObjects.get(s));
                            break;
                        }
                    }
                    s4 = JSONObject.parseObject(
                        buildObjectsAndRelationsContext.getRsponseMap().get(ApiKeyGkSceneEnum.CIRCLE.getType()),
                        List.class);
                    if (s4 == null) {
                        s4 = new ArrayList();
                    }
                    for (int i = 0; i < s4.size(); i++) {
                        List l = (List) s4.get(i);
                        // 圆的圆心名称
                        String o = (String) l.get(0);
                        if (l.size() >= 4 && Objects.equals(pointObjects.get(s).get("name"), o)) {
                            pointObjectsTmp.put(s, pointObjects.get(s));
                            break;
                        }
                    }

                    // lostPoint = true;
                    // pointObjectsTmp.put(s, pointObjects.get(s));
                }
            }
        }
        pointObjects = pointObjectsTmp;
        Set<String> pointNames = pointObjects.keySet();
        for (int i = buildObjectsAndRelationsContext.getInaccuratePoints().size() - 1; i >= 0; i--) {
            if (!pointNames.contains(buildObjectsAndRelationsContext.getInaccuratePoints().get(i).getName())) {
                lostPoint = true;
                buildObjectsAndRelationsContext.getInaccuratePoints().remove(i);

                // HashMap<String, String> hashMap = new HashMap<>();
                // Map point = new HashMap();
                // point.put("id", "d_" + buildObjectsAndRelationsParam.getIdCount().getAndIncrement());
                // point.put("category", "dot");
                // point.put("name", buildObjectsAndRelationsParam.getInaccuratePoints().get(i).getName());
                // point.put("type", "normal");
                // point.put("values", hashMap);
                // pointObjects.put(buildObjectsAndRelationsParam.getInaccuratePoints().get(i).getName(), point);
            }
        }
        if (lostPoint) {
            log.error("getAprGraphicKits 大模型抽点取比测绘结果少gkId {} {} {} {} ",
                buildObjectsAndRelationsContext.getGkId(),
                buildObjectsAndRelationsContext.getOcrImageUrl(),
                JSON.toJSONString(ptm),
                JSON.toJSONString(pointObjectsRaw));
            throw new RuntimeException("大模型抽点取比测绘结果少" + " " + buildObjectsAndRelationsContext.getGkId());
        }

        // 提取线段对象
        Map<String, Map> lineSegmentObjects = null;
        try {
            lineSegmentObjects = extractLineSegmentObjects(buildObjectsAndRelationsContext.getIdCount(),
                buildObjectsAndRelationsContext.getRsponseMap(), pointObjects);
        } catch (Exception e) {
            log.error("提线段对象失败 gkId {}", buildObjectsAndRelationsContext.getGkId(), e);
            throw new BizException("提线段对象失败" + " " + buildObjectsAndRelationsContext.getGkId());
        }
        // 提取射线对象
        Map<String, Map> rayObjects = null;
        try {
            rayObjects = extractRayObjects(buildObjectsAndRelationsContext.getRsponseMap(), pointObjects);
        } catch (Exception e) {
            log.error("提射线对象失败 gkId {}", buildObjectsAndRelationsContext.getGkId(), e);
            throw new BizException("提射线对象失败" + " " + buildObjectsAndRelationsContext.getGkId());
        }
        // 提直线对象
        Map<String, Map> straightLineObjects = null;
        try {
            straightLineObjects = extractStraightLineObjects(buildObjectsAndRelationsContext.getRsponseMap(),
                pointObjects);
        } catch (Exception e) {
            log.error("提直线对象失败 gkId {}", buildObjectsAndRelationsContext.getGkId(), e);
            throw new BizException("提直线对象失败" + " " + buildObjectsAndRelationsContext.getGkId());
        }

        // 提取角对象
        List<Map> angleObjects = null;
        try {
            angleObjects = extractAngleObjects(buildObjectsAndRelationsContext.getRsponseMap(), pointObjects);
        } catch (Exception e) {
            log.error("提取角对象失败 gkId {}", buildObjectsAndRelationsContext.getGkId(), e);
            throw new BizException("提取角对象失败" + " " + buildObjectsAndRelationsContext.getGkId());
        }

        // 提取三角形对象
        Map<String, Map> triangleObjects = null;
        try {
            triangleObjects = extractTriangleObjects(buildObjectsAndRelationsContext.getRsponseMap(),
                pointObjects);
        } catch (Exception e) {
            log.error("提取三角形对象失败 gkId {}", buildObjectsAndRelationsContext.getGkId(), e);
            throw new BizException("提取三角形对象失败" + " " + buildObjectsAndRelationsContext.getGkId());
        }

        // 提取四边形对象
        Map<String, Map> quadrilateralObjects = null;
        try {
            quadrilateralObjects = extractQuadrilateralObjects(
                buildObjectsAndRelationsContext.getRsponseMap(), pointObjects);
        } catch (Exception e) {
            log.error("提取四边形对象失败 gkId {}", buildObjectsAndRelationsContext.getGkId(), e);
            throw new BizException("提取四边形对象失败" + " " + buildObjectsAndRelationsContext.getGkId());
        }

        // 提取多边形对象
        Map<String, Map> polygonObjects = null;
        try {
            polygonObjects = extractPolygonObjects(buildObjectsAndRelationsContext.getRsponseMap(),
                pointObjects);
        } catch (Exception e) {
            log.error("提取多边形对象失败 gkId {}", buildObjectsAndRelationsContext.getGkId(), e);
            throw new BizException("提取多边形对象失败" + " " + buildObjectsAndRelationsContext.getGkId());
        }

        // 提取圆弧对象
        Map<String, Map> circularArcObjects = null;
        try {
            circularArcObjects = extractCircularArcObjects(buildObjectsAndRelationsContext.getRsponseMap(),
                pointObjects);
        } catch (Exception e) {
            log.error("提取圆弧对象失败 gkId {}", buildObjectsAndRelationsContext.getGkId(), e);
            throw new BizException("提取圆弧对象失败" + " " + buildObjectsAndRelationsContext.getGkId());
        }

        // 提取圆规弧对象(修改将三个点得变成俩个点)
        Map<String, Map> compassArcObjects = null;
        try {
            compassArcObjects = extractCompassArcObjects(buildObjectsAndRelationsContext.getRsponseMap(),
                pointObjects);
        } catch (Exception e) {
            log.error("提取圆规弧对象失败 gkId {}", buildObjectsAndRelationsContext.getGkId(), e);
            throw new BizException("提取圆规弧对象失败" + " " + buildObjectsAndRelationsContext.getGkId());
        }

        // 提取圆对象
        Map<String, Map> circleObjects = null;
        try {
            circleObjects = extractCircleObjects(buildObjectsAndRelationsContext.getRsponseMap(),
                pointObjects);
        } catch (Exception e) {
            log.error("提取圆对象失败 gkId {}", buildObjectsAndRelationsContext.getGkId(), e);
            throw new BizException("提取圆对象失败" + " " + buildObjectsAndRelationsContext.getGkId());
        }

        // 提取数轴对象
        Map<String, Map> numberLineObjects = null;
        try {
            numberLineObjects = extractNumberLineObjects(buildObjectsAndRelationsContext.getRsponseMap(),
                pointObjects);
        } catch (Exception e) {
            log.error("提取数轴对象失败 gkId {}", buildObjectsAndRelationsContext.getGkId(), e);
            throw new BizException("提取数轴对象失败" + " " + buildObjectsAndRelationsContext.getGkId());
        }

        // 提取二维坐标系对象
        Map<String, Map> cartesianCoordinateSystemObjects = null;
        try {
            cartesianCoordinateSystemObjects = extractCartesianCoordinateSystemObjects(
                buildObjectsAndRelationsContext.getRsponseMap(),
                pointObjects, buildObjectsAndRelationsContext.getInaccuratePoints());
        } catch (Exception e) {
            log.error("提取二维坐标系对象失败 gkId {}", buildObjectsAndRelationsContext.getGkId(), e);
            throw new BizException("提取二维坐标系对象失败" + " " + buildObjectsAndRelationsContext.getGkId());
        }

        // // // 提取抛物线对象
        // Map<String, Map> parabolaObjects = extractParabolaObjects(buildObjectsAndRelationsParam.getRsponseMap(),
        //     pointObjects,buildObjectsAndRelationsParam.getInaccuratePoints());

        // 提取反比例双曲线对象
        Map<String, Map> inverseProportionHyperbolaObjects = null;
        try {
            inverseProportionHyperbolaObjects = extractInverseProportionHyperbolaObjects(
                buildObjectsAndRelationsContext.getRsponseMap(),
                pointObjects);
        } catch (Exception e) {
            log.error("提取反比例双曲线对象失败 gkId {}", buildObjectsAndRelationsContext.getGkId(), e);
            throw new BizException("提取反比例双曲线对象失败" + " " + buildObjectsAndRelationsContext.getGkId());
        }

        //-------------------------------------------------
        // 修复线段提取，根据三角形、四边形、多边形 共线关系提取线段,圆弧，圆规弧，圆
        fixLineSegmentObjects(buildObjectsAndRelationsContext.getIdCount(), pointObjects, lineSegmentObjects,
            buildObjectsAndRelationsContext.getLineInfoList(), angleObjects, triangleObjects,
            quadrilateralObjects, polygonObjects, circularArcObjects, circleObjects, compassArcObjects);

        Map<String, List<Set<String>>> determiningPoints = collectDeterminingPoints(
            angleObjects, triangleObjects, quadrilateralObjects, polygonObjects,
            circularArcObjects, circleObjects);

        // 提取点类关系
        List<Map> pointRelations = null;
        try {
            pointRelations = extractPointRelations(buildObjectsAndRelationsContext.getIdCount(),
                buildObjectsAndRelationsContext.getRsponseMap(),
                pointObjects,
                buildObjectsAndRelationsContext.getLineInfoList(), determiningPoints);
        } catch (Exception e) {
            log.error("提取点类关系失败 gkId {}", buildObjectsAndRelationsContext.getGkId(), e);
            throw new BizException("提取点类关系失败" + " " + buildObjectsAndRelationsContext.getGkId());
        }

        // 提取线类关系
        List<Map> lineRelations = null;
        try {
            lineRelations = extractLineRelations(buildObjectsAndRelationsContext.getIdCount(),
                buildObjectsAndRelationsContext.getRsponseMap(),
                lineSegmentObjects, quadrilateralObjects);
        } catch (Exception e) {
            log.error("提取线类关系失败 gkId {}", buildObjectsAndRelationsContext.getGkId(), e);
            throw new BizException("提取线类关系失败" + " " + buildObjectsAndRelationsContext.getGkId());
        }

        // 提取角类关系
        List<Map> angleRelations = null;
        try {
            angleRelations = extractAngleRelations(buildObjectsAndRelationsContext.getIdCount(),
                buildObjectsAndRelationsContext.getRsponseMap(),
                angleObjects);
        } catch (Exception e) {
            log.error("提取角类关系失败 gkId {}", buildObjectsAndRelationsContext.getGkId(), e);
            throw new BizException("提取角类关系失败" + " " + buildObjectsAndRelationsContext.getGkId());
        }

        // 提取三角形类关系
        // List<Map> triangleRelations = null;
        // try {
        //     triangleRelations = extractTriangleRelations(buildObjectsAndRelationsContext.getIdCount(),
        //         buildObjectsAndRelationsContext.getRsponseMap(), angleObjects);
        // } catch (Exception e) {
        //     log.error("提取三角形类关系失败 gkId {}", buildObjectsAndRelationsContext.getGkId(), e);
        //     throw new BizException("提取三角形类关系失败"+" "+buildObjectsAndRelationsContext.getGkId());
        // }

        //-------------------------------------------------
        // 修复一个字母名称角对象特征点
        fixAngleObjectsDeterminingPoints(angleObjects, triangleObjects, quadrilateralObjects, polygonObjects,
            pointObjects, lineSegmentObjects, pointRelations);
        //-------------------------------------------------

        // 组装结果
        Map<String, List<Map>> resultMap = new HashMap<>();
        List<Map> objects = new ArrayList<>();
        resultMap.put("objects", objects);
        List<Map> relations = new ArrayList<>();
        resultMap.put("relations", relations);

        buildObjectsAndRelationsContext.getPointObjects().putAll(pointObjects);
        objects.addAll(pointObjects.values());
        buildObjectsAndRelationsContext.getLineSegmentObjects().putAll(lineSegmentObjects);
        objects.addAll(lineSegmentObjects.values());
        objects.addAll(rayObjects.values());
        buildObjectsAndRelationsContext.getStraightLineObjects().putAll(straightLineObjects);
        objects.addAll(straightLineObjects.values());
        objects.addAll(angleObjects);
        objects.addAll(triangleObjects.values());

        objects.addAll(quadrilateralObjects.values());
        objects.addAll(polygonObjects.values());
        objects.addAll(circularArcObjects.values());
        objects.addAll(compassArcObjects.values());
        objects.addAll(circleObjects.values());
        objects.addAll(numberLineObjects.values());
        buildObjectsAndRelationsContext.getCartesianCoordinateSystemObjects().putAll(cartesianCoordinateSystemObjects);
        objects.addAll(cartesianCoordinateSystemObjects.values());
        // objects.addAll(parabolaObjects.values());
        objects.addAll(inverseProportionHyperbolaObjects.values());

        relations.addAll(pointRelations);
        relations.addAll(lineRelations);
        relations.addAll(angleRelations);
        // relations.addAll(triangleRelations);

        return resultMap;
    }

    /**
     * 获取抛物线参数
     *
     * @param rsponseMap
     * @param pointObjects
     * @param inaccuratePoints
     * @return
     */

    @Override
    public Map<String, Map> extractParabolaObjects(ConcurrentHashMap<String, String> rsponseMap,
        Map<String, Map> pointObjects, List<PointSparnll> inaccuratePoints) {
        List<PointSparnll> pointList = new ArrayList<>();
        Map<String, Map> polygonObjects = new HashMap<>();
        List s4 = JSONObject.parseObject(rsponseMap.get(ApiKeyGkSceneEnum.PARABOLA.getType()), List.class);
        if (s4 == null) {
            s4 = new ArrayList();
        }
        // 获取抛物线a，b，c系数
        // List dpl = new ArrayList<>();
        int count4 = 1;
        for (int i = 0; i < s4.size(); i++) {
            List l = (List) s4.get(i);
            // 元素1. 抛物线的表达式（如："y=3(x-3)^2+4"）
            // 优先保存有系数的表达式，没有则继续保存系数为未知数的，例如y=ax²+bx+c；
            // 服务端需要自己判断是否是能直接用来画图
            // 元素2. 抛物线的顶点（如："A"）
            // 元素3. 抛物线的对称轴（如："x=3"）
            // 元素4. 所有已知的在该抛物线上任意非顶点的点（如："B"）
            // 抛物线的表达式
            String o = (String) l.get(0);
            double[] doublePointList = getDoublePointList(inaccuratePoints, s4, pointList, pointObjects);
            if (Objects.nonNull(doublePointList)) {
                Map point = new HashMap();
                point.put("id", "pa_" + count4);
                count4++;
                point.put("category", "parabola");
                point.put("name", "parabola");
                HashMap hashMap = new HashMap();
                hashMap.put("a", doublePointList[0]);
                hashMap.put("b", doublePointList[1]);
                hashMap.put("c", doublePointList[2]);
                point.put("values", hashMap);
                // point.put("value", o);
                List dpl = new ArrayList<>();
                for (int j = 1; j < l.size(); j++) {
                    if (j == 2) {
                        continue;
                    }
                    Map p = new HashMap();

                    if (pointObjects.get(LetterExtractor.extractLetters((String) l.get(j))) != null) {
                        p.put("id", pointObjects.get(LetterExtractor.extractLetters((String) l.get(j))).get("id"));
                        dpl.add(p);
                    } else {
                        log.error("丢弃抛物线 {} 没有点 {} points:{}", o, l.get(j),
                            JSONObject.toJSONString(pointObjects));
                    }
                }
                point.put("determining_points", dpl);
                polygonObjects.put("parabola", point);
            }
        }
        // polygonObjects.put("",point);
        return polygonObjects;
    }

    /**
     * 收集不可能共线点关系集合
     *
     * @param angleObjects
     * @param triangleObjects
     * @param quadrilateralObjects
     * @param polygonObjects
     * @param circularArcObjects
     * @param circleObjects
     * @return
     */
    private Map<String, List<Set<String>>> collectDeterminingPoints(
        List<Map> angleObjects,
        Map<String, Map> triangleObjects,
        Map<String, Map> quadrilateralObjects,
        Map<String, Map> polygonObjects,
        Map<String, Map> circularArcObjects,
        Map<String, Map> circleObjects) {

        Map<String, List<Set<String>>> determiningPointsMap = new HashMap<>();

        // Collect from angle objects
        for (Map angle : angleObjects) {
            List<Map> points = (List<Map>) angle.get("determining_points");
            if (points != null && points.size() > 0) {
                Set<String> pointIds = points.stream()
                    .map(p -> (String) p.get("id"))
                    .collect(Collectors.toSet());

                for (Map point : points) {
                    String pointId = (String) point.get("id");
                    determiningPointsMap.computeIfAbsent(pointId, k -> new ArrayList<>())
                        .add(pointIds);
                }
            }
        }

        // Collect from triangle objects
        for (Map.Entry<String, Map> entry : triangleObjects.entrySet()) {
            List<Map> points = (List<Map>) entry.getValue().get("determining_points");
            if (points != null && points.size() > 0) {
                Set<String> pointIds = points.stream()
                    .map(p -> (String) p.get("id"))
                    .collect(Collectors.toSet());

                for (Map point : points) {
                    String pointId = (String) point.get("id");
                    determiningPointsMap.computeIfAbsent(pointId, k -> new ArrayList<>())
                        .add(pointIds);
                }
            }
        }

        // Collect from quadrilateral objects
        for (Map.Entry<String, Map> entry : quadrilateralObjects.entrySet()) {
            List<Map> points = (List<Map>) entry.getValue().get("determining_points");
            if (points != null && points.size() > 0) {
                Set<String> pointIds = points.stream()
                    .map(p -> (String) p.get("id"))
                    .collect(Collectors.toSet());

                for (Map point : points) {
                    String pointId = (String) point.get("id");
                    determiningPointsMap.computeIfAbsent(pointId, k -> new ArrayList<>())
                        .add(pointIds);
                }
            }
        }

        // Collect from polygon objects
        for (Map.Entry<String, Map> entry : polygonObjects.entrySet()) {
            List<Map> points = (List<Map>) entry.getValue().get("determining_points");
            if (points != null && points.size() > 0) {
                Set<String> pointIds = points.stream()
                    .map(p -> (String) p.get("id"))
                    .collect(Collectors.toSet());

                for (Map point : points) {
                    String pointId = (String) point.get("id");
                    determiningPointsMap.computeIfAbsent(pointId, k -> new ArrayList<>())
                        .add(pointIds);
                }
            }
        }

        // Collect from circular arc objects (skip first determining point)
        for (Map.Entry<String, Map> entry : circularArcObjects.entrySet()) {
            List<Map> points = (List<Map>) entry.getValue().get("determining_points");
            if (points != null && points.size() > 1) {
                // 跳过圆心点，只收集圆弧上的点
                Set<String> pointIds = points.stream()
                    .skip(1)
                    .map(p -> (String) p.get("id"))
                    .collect(Collectors.toSet());

                for (int i = 1; i < points.size(); i++) {
                    Map point = points.get(i);
                    String pointId = (String) point.get("id");
                    determiningPointsMap.computeIfAbsent(pointId, k -> new ArrayList<>())
                        .add(pointIds);
                }
            }
        }

        // Collect from circle objects (skip first determining point)
        for (Map.Entry<String, Map> entry : circleObjects.entrySet()) {
            List<Map> points = (List<Map>) entry.getValue().get("determining_points");
            if (points != null && points.size() > 1) {
                // 跳过圆心点，只收集圆上的点
                Set<String> pointIds = points.stream()
                    .skip(1)
                    .map(p -> (String) p.get("id"))
                    .collect(Collectors.toSet());

                for (int i = 1; i < points.size(); i++) {
                    Map point = points.get(i);
                    String pointId = (String) point.get("id");
                    determiningPointsMap.computeIfAbsent(pointId, k -> new ArrayList<>())
                        .add(pointIds);
                }
            }
        }

        return determiningPointsMap;
    }

    // 抛物线
    private double[] getDoublePointList(List<PointSparnll> inaccuratePoints, List s4, List<PointSparnll> pointList,
        Map<String, Map> pointObjects) {
        double[] doubles = null;
        if (s4.isEmpty()) {
            return doubles;
        }
        List list = (List) s4.get(0);
        if (list.size() >= 3) {
            double[] doubles2 = processVertex(list, inaccuratePoints, pointList, pointObjects);
            if (Objects.nonNull(doubles2)) {
                return doubles2;
            }
            // 代表已经拿到顶点方程
            if (pointList.size() == 1) {
                processListElements(list, inaccuratePoints, pointList);
                if (pointList.size() == 3) {
                    // 计算偏离最大的值坐标
                    PointSparnll moreDeviatedPoint = SymmetricPointCalculator.findMoreDeviatedPoint(
                        pointList.get(0), pointList.get(1),
                        pointList.get(2));
                    // pointList列表种排除moreDeviatedPoint
                    List<PointSparnll> finallyPointList = pointList.stream()
                        .filter(pointSparnll -> !pointSparnll.getName().equals(moreDeviatedPoint.getName()))
                        .collect(Collectors.toList());
                    // 计算另一个点
                    double[] center = new double[]{finallyPointList.get(0).getCoord().get(0).doubleValue(),
                        finallyPointList.get(0).getCoord().get(1).doubleValue()};
                    double[] point = new double[]{finallyPointList.get(1).getCoord().get(0).doubleValue(),
                        finallyPointList.get(1).getCoord().get(1).doubleValue()};
                    // 计算出第三个点
                    List<Double> doubles1 = SymmetricPointCalculator.calculateSymmetricPoint(center, point);
                    double[] A = {doubles1.get(0),
                        doubles1.get(1)};
                    double[] B = {center[0],
                        center[1]};
                    double[] C = {point[0],
                        point[0]};
                    return ParabolaSolver.solveParabola(A, B, C);
                }
            }
        }
        processListElements(list, inaccuratePoints, pointList);
        if (pointList.size() < 3 && pointList.size() >= 2) {
            if (pointList.size() == 2 && StringUtils.isNotBlank((String) list.get(1))) {
                ArrayList<Double> doubles2 = calculateSymmetricPointIfPossible(list, pointList);
                if (doubles2 != null) {
                    doubles = solveParabolaWithSymmetricPoint(pointList, doubles2);
                    return doubles;
                }
            }
            return null;
        } else if (pointList.size() >= 3) {
            doubles = solveParabolaWithThreePoints(pointList);
            return doubles;
        }
        return doubles;
    }

    private double[] processVertex(List list, List<PointSparnll> inaccuratePoints, List<PointSparnll> pointList,
        Map<String, Map> pointObjects) {
        String o1 = (String) list.get(1);
        // ①计算顶点坐标
        if (StringUtils.isNotBlank(o1)) {
            String topO1 = LetterExtractor.extractLetters(o1);
            for (PointSparnll inaccuratePoint : inaccuratePoints) {
                if (inaccuratePoint.getName().equals(topO1)) {
                    pointList.add(inaccuratePoint);
                }
            }
            return null;
        }
        List<PointSparnll> points = new ArrayList<>();
        // ②:判断是否存在对称轴
        if (StringUtils.isNotBlank((String) list.get(2))) {
            // 查看模型返回的点是是否在点坐标中
            List list1 = list.subList(3, list.size());
            // 模型和点坐标共同拥有的数据
            HashMap<String, Map> objectObjectHashMap = new HashMap<>();
            for (Object o : list1) {
                Map map = pointObjects.get(o);
                if (Objects.nonNull(map)) {
                    objectObjectHashMap.put((String) o, map);

                }
            }
            // ①:根据对称轴计算对称点
            if (objectObjectHashMap.size() == 2) {
                for (PointSparnll inaccuratePoint : inaccuratePoints) {
                    Map map = objectObjectHashMap.get(inaccuratePoint.getName());
                    if (Objects.nonNull(map)) {
                        points.add(inaccuratePoint);
                    }
                }
                for (PointSparnll point : points) {
                    Map map = objectObjectHashMap.get(point.getName());
                    if (Objects.isNull(map.get("qx"))) {
                        break;
                    }
                    double v = points.get(1).getCoord().get(0).doubleValue();
                    double w = points.get(1).getCoord().get(1).doubleValue();
                    Map map1 = objectObjectHashMap.get(points.get(1).getName());
                    if (Objects.isNull(map1.get("qx"))) {
                        break;
                    }
                    double vx = (double) map1.get("qx");
                    double wy = (double) map1.get("qy");
                    // 点坐标x
                    double qx = (double) map.get("qx");
                    // 点坐标y
                    double qy = (double) map.get("qy");
                    // 对称轴
                    String o = (String) list.get(2);
                    Argument x1 = new Argument(o);
                    // 当前点测绘坐标x
                    double x = point.getCoord().get(0).doubleValue();
                    double y = point.getCoord().get(1).doubleValue();
                    if (x1.getArgumentName().equals("x")) {
                        // x周每1各单位的比例尺是多少
                        double scalex = (v - x) / (vx - qx);
                        // 对称轴的坐标值(转成正的)
                        // double scale = Math.abs((qx-argumentValue)*scalex)+x;
                        // 对称点的坐标
                        double x2 = ((2 * x1.getArgumentValue() - qx) - qx) * scalex + x;

                        double y2 = y;
                        // 计算对称点坐标
                        PointSparnll pointSparnll = new PointSparnll();
                        pointSparnll.setName("Ax");
                        List<BigDecimal> coord = new ArrayList<>();
                        coord.add(BigDecimal.valueOf(x2));
                        coord.add(BigDecimal.valueOf(y2));
                        pointSparnll.setCoord(coord);
                        points.add(pointSparnll);
                        break;
                    } else {
                        // x周每1各单位的比例尺是多少
                        double scaley = (w - y) / (wy - qy);
                        // 对称轴的坐标值
                        // double scale = Math.abs((qy-argumentValue)*scaley)+y;
                        // 对称点的坐标
                        double x2 = x;
                        double y2 = ((2 * x1.getArgumentValue() - qy) - qy) * scaley + y;

                        // 计算对称轴坐标
                        PointSparnll pointSparnll = new PointSparnll();
                        pointSparnll.setName("Ay");
                        List<BigDecimal> coord = new ArrayList<>();
                        coord.add(BigDecimal.valueOf(x2));
                        coord.add(BigDecimal.valueOf(y2));
                        pointSparnll.setCoord(coord);
                        points.add(pointSparnll);
                    }
                }
            }

            if (points.size() >= 3) {
                double[] point1 = {points.get(0).getCoord().get(0).doubleValue(),
                    points.get(0).getCoord().get(1).doubleValue()};
                double[] point2 = {points.get(1).getCoord().get(0).doubleValue(),
                    points.get(1).getCoord().get(1).doubleValue()};
                double[] point3 = {points.get(2).getCoord().get(0).doubleValue(),
                    points.get(2).getCoord().get(1).doubleValue()};

                return ParabolaSolver.solveParabola(point1, point2, point3);
            }
            // // ③抛物线解析式
            // if (StringUtils.isNotBlank((String) list.get(0))){
            //
            //     PointSparnll pointSparnll = new PointSparnll();
            //     List<BigDecimal> coord = new ArrayList<>();
            //
            //     // 抛物线解析式
            //     String s =  (String) list.get(0);
            //     double[] pointA = {points.get(0).getCoord().get(0).doubleValue(), points.get(0).getCoord().get(1).doubleValue()};
            //     double[] pointB = {points.get(1).getCoord().get(0).doubleValue(), points.get(1).getCoord().get(1).doubleValue()};
            //     s = s.replaceAll("\\{", "").replaceAll("\\}", "");
            //     CoefficientResult coefficientResult = extractCoefficients(s);
            //     String a = coefficientResult.getA();
            //     VertexResult vertexResult = ParabolaVertexCalculator.calculateVertex(Double.parseDouble(a), pointA[0],
            //         pointA[1], pointB[0], pointB[1]);
            //     ParabolaResult parabolaResult = ParabolaEquationSolver.solveParabola(s, pointA, pointB);
            //     if (Objects.nonNull(vertexResult)){
            //         pointSparnll.setName("Ax");
            //         coord.add(BigDecimal.valueOf(vertexResult.getVertexX()));
            //         coord.add(BigDecimal.valueOf(vertexResult.getVertexY()));
            //         pointSparnll.setCoord(coord);
            //         points.add(pointSparnll);
            //     }
            //     if (points.size() == 3){
            //         double[] point1 = {points.get(0).getCoord().get(0).doubleValue(),
            //             points.get(0).getCoord().get(1).doubleValue()};
            //         double[] point2 = {points.get(1).getCoord().get(0).doubleValue(),
            //             points.get(1).getCoord().get(1).doubleValue()};
            //         double[] point3 = {points.get(2).getCoord().get(0).doubleValue(),
            //             points.get(2).getCoord().get(1).doubleValue()};
            //         return ParabolaSolver.solveParabola(point1, point2, point3);
            //     }
            //     return  null;
            // }
            return null;
        }

        return null;

    }

    private void processListElements(List list, List<PointSparnll> inaccuratePoints, List<PointSparnll> pointList) {
        List list1 = list.subList(3, list.size());
        for (int i = 0; i < list1.size(); i++) {
            String o = (String) list1.get(i);
            if (StringUtils.isBlank(o)) {
                continue;
            }
            String s = LetterExtractor.extractLetters(o);
            if (StringUtils.isBlank(s) || "O".equals(s) || "o".equals(s)) {
                continue;
            }
            for (PointSparnll inaccuratePoint : inaccuratePoints) {
                if (inaccuratePoint.getName().equals(s)) {
                    pointList.add(inaccuratePoint);
                }
            }
        }
    }

    private ArrayList<Double> calculateSymmetricPointIfPossible(List list, List<PointSparnll> pointList) {
        String topO1 = LetterExtractor.extractLetters((String) list.get(1));
        double[] center = null;
        double[] point = null;
        for (PointSparnll pointSparnll : pointList) {
            if (pointSparnll.getName().equals(topO1)) {

                center = new double[]{pointSparnll.getCoord().get(0).doubleValue(),
                    pointSparnll.getCoord().get(1).doubleValue()};
            } else {
                point = new double[]{pointSparnll.getCoord().get(0).doubleValue(),
                    pointSparnll.getCoord().get(1).doubleValue()};
            }
        }
        if (center != null && point != null) {
            return SymmetricPointCalculator.calculateSymmetricPoint(center, point);
        }
        return null;
    }

    private double[] solveParabolaWithSymmetricPoint(List<PointSparnll> pointList, ArrayList<Double> doubles2) {
        double[] point1 = {doubles2.get(0), doubles2.get(1)};
        double[] point2 = {pointList.get(0).getCoord().get(0).doubleValue(),
            pointList.get(0).getCoord().get(1).doubleValue()};
        double[] point3 = {pointList.get(1).getCoord().get(0).doubleValue(),
            pointList.get(1).getCoord().get(1).doubleValue()};
        return ParabolaSolver.solveParabola(point1, point2, point3);
    }

    private double[] solveParabolaWithThreePoints(List<PointSparnll> pointList) {
        double[] point1 = {pointList.get(0).getCoord().get(0).doubleValue(),
            pointList.get(0).getCoord().get(1).doubleValue()};
        double[] point2 = {pointList.get(1).getCoord().get(0).doubleValue(),
            pointList.get(1).getCoord().get(1).doubleValue()};
        double[] point3 = {pointList.get(2).getCoord().get(0).doubleValue(),
            pointList.get(2).getCoord().get(1).doubleValue()};
        return ParabolaSolver.solveParabola(point1, point2, point3);
    }


    public static String preprocessParamName(String param) {
        return param.replaceAll("[^a-zA-Z0-9_]", "_");
    }

    /**
     * 提取点对象
     *
     * @param rsponseMap
     * @return
     */
    private Map<String, Map> extractPointObjects(AtomicInteger idCount, Map<String, String> rsponseMap) {
        Map<String, Map> pointObjects = new HashMap<>();

        List s = JSONObject.parseObject(rsponseMap.get(ApiKeyGkSceneEnum.POINT_OBJECT.getType()), List.class);
        if (s == null) {
            s = new ArrayList();
        }
        Set<String> nameSet = new HashSet<>();

        for (int i = 0; i < s.size(); i++) {
            HashMap<String, String> hashMap = new HashMap<>();
            Object o = s.get(i);

            if (o instanceof String) {
                // 根据名称排除重复
                if (nameSet.contains(o)) {
                    continue;
                } else {
                    nameSet.add((String) o);
                }

                Map point = new HashMap();
                point.put("id", "d_" + idCount.getAndIncrement());
                point.put("category", "dot");
                point.put("name", o);
                point.put("type", "normal");
                // ["A",[1,2],"B","C"]
                if (i + 1 < s.size() && s.get(i + 1) instanceof List) {
                    hashMap.put("coord", charFilter(
                        ((List) s.get(i + 1)).get(0).toString() + "," + ((List) s.get(i + 1)).get(1).toString()));
                    // 将(List)s.get(i + 1)改成字符串
                    point.put("qx", new Expression(((List) s.get(i + 1)).get(0).toString()).calculate());
                    point.put("qy", new Expression(((List) s.get(i + 1)).get(1).toString()).calculate());
                }
                point.put("values", hashMap);
                pointObjects.put((String) o, point);
            }
        }
        return pointObjects;
    }

    /**
     * 提取线段对象
     *
     * @param rsponseMap
     * @param pointObjects
     * @return
     */
    @NotNull
    private Map<String, Map> extractLineSegmentObjects(AtomicInteger idCount, Map<String, String> rsponseMap,
        Map<String, Map> pointObjects) {
        Map<String, Map> lineObjects = new HashMap();
        List s2 = JSONObject.parseObject(rsponseMap.get(ApiKeyGkSceneEnum.LINE_SEGMENT_OBJECT.getType()), List.class);
        if (s2 == null) {
            s2 = new ArrayList();
        }
        Set<String> nameSet2 = new HashSet<>();
        for (int i = 0; i < s2.size(); i++) {
            List l = (List) s2.get(i);
            String o = (String) l.get(0);
            String o1 = (String) l.get(1);
            String o2 = (String) l.get(2);

            // 根据名称排除重复
            Boolean isSame = false;
            for (String s : nameSet2) {
                if (isSameLine(s, o)) {
                    isSame = true;
                    break;
                }
            }
            if (isSame) {
                continue;
            }
            Map lengthMap = new HashMap();
            Map point = new HashMap();
            point.put("id", "s_" + idCount.getAndIncrement());
            point.put("category", "segment");
            point.put("name", o);
            point.put("type", "normal");
            point.put("value", charFilter(o1));
            lengthMap.put("length", charFilter(o1));
            point.put("values", lengthMap);
            point.put("shortName", o2);
            List dpl = new ArrayList<>();
            point.put("determining_points", dpl);
            List<String> points = splitPointNameList(o);
            boolean noPoint = false;
            for (int j = 0; j < points.size(); j++) {
                Map p = new HashMap();
                if (pointObjects.get(points.get(j)) != null) {
                    p.put("id", pointObjects.get(points.get(j)).get("id"));
                    dpl.add(p);
                } else {
                    noPoint = true;
                    log.error("丢弃线段 {} 没有点 {} points:{}", o, points.get(j),
                        JSONObject.toJSONString(pointObjects));
                }
            }
            if (noPoint) {
                continue;
            }
            nameSet2.add(o);
            lineObjects.put(o, point);
        }
        return lineObjects;
    }

    /**
     * 提取射线对象
     *
     * @param rsponseMap
     * @param pointObjects
     * @return
     */
    @NotNull
    private Map<String, Map> extractRayObjects(Map<String, String> rsponseMap, Map<String, Map> pointObjects) {
        Map<String, Map> lineObjects = new HashMap();
        List s2 = JSONObject.parseObject(rsponseMap.get(ApiKeyGkSceneEnum.RAY.getType()), List.class);
        if (s2 == null) {
            s2 = new ArrayList();
        }
        int count2 = 1;
        Set<String> nameSet2 = new HashSet<>();
        for (int i = 0; i < s2.size(); i++) {
            List l = (List) s2.get(i);
            String o = (String) l.get(0);
            String o1 = (String) l.get(1);

            // 根据名称排除重复
            Boolean isSame = false;
            for (String s : nameSet2) {
                if (isSameRayLine(s, o)) {
                    isSame = true;
                    break;
                }
            }
            if (isSame) {
                continue;
            }

            Map point = new HashMap();
            point.put("id", "rl_" + count2);
            count2++;
            point.put("category", "ray");
            point.put("name", o);
            point.put("type", "normal");
            point.put("shortName", o1);
            List dpl = new ArrayList<>();
            point.put("determining_points", dpl);
            List<String> points = splitPointNameList(o);
            if (points.size() < 2) {
                continue;
            }
            // 射线取首尾字母
            points = new ArrayList<>(List.of(new String[]{points.get(0), points.get(points.size() - 1)}));
            boolean noPoint = false;
            for (int j = 0; j < points.size(); j++) {
                Map p = new HashMap();
                if (pointObjects.get(points.get(j)) != null) {
                    p.put("id", pointObjects.get(points.get(j)).get("id"));
                    dpl.add(p);
                } else {
                    noPoint = true;
                    log.error("丢弃射线 {} 没有点 {} points:{}", o, points.get(j),
                        JSONObject.toJSONString(pointObjects));
                }
            }
            if (noPoint) {
                continue;
            }
            nameSet2.add(o);
            lineObjects.put(o, point);
        }
        return lineObjects;
    }

    /**
     * 提直线对象
     *
     * @param rsponseMap
     * @param pointObjects
     * @return
     */
    @NotNull
    private Map<String, Map> extractStraightLineObjects(Map<String, String> rsponseMap, Map<String, Map> pointObjects) {
        Map<String, Map> lineObjects = new HashMap();
        List s2 = JSONObject.parseObject(rsponseMap.get(ApiKeyGkSceneEnum.STRAIGHT_LINE.getType()), List.class);
        if (s2 == null) {
            s2 = new ArrayList();
        }
        int count2 = 1;
        Set<String> nameSet2 = new HashSet<>();
        for (int i = 0; i < s2.size(); i++) {
            List l = (List) s2.get(i);
            String o = (String) l.get(0);
            if (splitPointNameList(o).size() >= 2) {
                String o1 = (String) l.get(1);

                // 根据名称排除重复
                Boolean isSame = false;
                for (String s : nameSet2) {
                    if (isSameLine(s, o)) {
                        isSame = true;
                        break;
                    }
                }
                if (isSame) {
                    continue;
                }

                Map point = new HashMap();
                point.put("id", "sl_" + count2);
                count2++;
                point.put("category", "straightLine");
                point.put("name", o);
                point.put("type", "normal");
                point.put("shortName", o1);
                List dpl = new ArrayList<>();
                point.put("determining_points", dpl);
                List<String> points = splitPointNameList(o);
                points = new ArrayList<>(List.of(new String[]{points.get(0), points.get(points.size() - 1)}));
                boolean noPoint = false;
                for (int j = 0; j < points.size(); j++) {
                    Map p = new HashMap();
                    if (pointObjects.get(points.get(j)) != null) {
                        p.put("id", pointObjects.get(points.get(j)).get("id"));
                        dpl.add(p);
                    } else {
                        noPoint = true;
                        log.error("丢弃直线 {} 没有点 {} points:{}", o, points.get(j),
                            JSONObject.toJSONString(pointObjects));
                    }
                }
                if (noPoint) {
                    continue;
                }
                nameSet2.add(o);
                lineObjects.put(o, point);
            }
        }
        return lineObjects;
    }

    /**
     * 提解析几何直线对象
     *
     * @param rsponseMap
     * @return
     */
    @Override
    public Map<String, Map> extractAnalyticGeometryStraightLineObjects(
        BuildObjectsAndRelationsContext buildObjectsAndRelationsContext, Map<String, String> rsponseMap,
        List<PointSparnll> pointDTOS) {
        List s2 = JSONObject.parseObject(rsponseMap.get(ApiKeyGkSceneEnum.STRAIGHT_LINE.getType()), List.class);
        if (s2 == null) {
            s2 = new ArrayList();
        }

        Map<String, String> idNameMap = new HashMap<>();
        buildObjectsAndRelationsContext.getPointObjects().forEach((k, v) -> {
            idNameMap.put(v.get("id").toString(), v.get("name").toString());
        });
        for (int i = 0; i < s2.size(); i++) {
            List l = (List) s2.get(i);
            String o = (String) l.get(0);
            // 没有点或者点少于2的直线根据方程处理
            if (splitPointNameList(o).size() < 2) {
                String o1 = (String) l.get(1);
                // 方程
                String o2 = (String) l.get(2);

                Map oPoint = null;// buildObjectsAndRelationsParam.getPointObjects().get("O")
                if (!buildObjectsAndRelationsContext.getCartesianCoordinateSystemObjects().values().isEmpty()) {
                    oPoint = buildObjectsAndRelationsContext.getPointObjects().get(idNameMap.get(
                        ((Map) ((List) new ArrayList<>(
                            buildObjectsAndRelationsContext.getCartesianCoordinateSystemObjects().values())
                            .get(0).get("determining_points")).get(0)).get("id").toString()));
                }
                if (oPoint == null) {
                    break;
                }
                String oName = oPoint.get("name").toString();
                PointSparnll pointSparnll = pointDTOS.stream().filter(p -> p.getName().equals(oName)).findFirst()
                    .orElse(null);
                if (pointSparnll == null) {
                    break;
                }

                if (new ArrayList<>(
                    buildObjectsAndRelationsContext.getCartesianCoordinateSystemObjects().values())
                    .get(0).get("scale") == null) {
                    break;
                }
                BigDecimal scale = new BigDecimal(new ArrayList<>(
                    buildObjectsAndRelationsContext.getCartesianCoordinateSystemObjects().values())
                    .get(0).get("scale").toString());

                String p1 = UUID.randomUUID().toString().replaceAll("-", "");
                String p2 = UUID.randomUUID().toString().replaceAll("-", "");
                Map point = new HashMap();
                point.put("id", "d_" + buildObjectsAndRelationsContext.getIdCount().getAndIncrement());
                point.put("category", "dot");
                point.put("name", p1);
                point.put("type", "hidden");
                point.put("values", new HashMap<>());
                buildObjectsAndRelationsContext.getPointObjects().put(p1, point);
                Map point2 = new HashMap();
                point2.put("id", "d_" + buildObjectsAndRelationsContext.getIdCount().getAndIncrement());
                point2.put("category", "dot");
                point2.put("name", p2);
                point2.put("type", "hidden");
                point2.put("values", new HashMap<>());
                buildObjectsAndRelationsContext.getPointObjects().put(p2, point2);

                Map straightLine = new HashMap();
                straightLine.put("id", "sl_" + buildObjectsAndRelationsContext.getIdCount().getAndIncrement());
                straightLine.put("category", "straightLine");
                straightLine.put("name", o2);
                straightLine.put("type", "analyticGeometry");
                straightLine.put("shortName", o2);
                List dpl = new ArrayList<>();
                straightLine.put("determining_points", dpl);

                Map p = new HashMap();
                p.put("id", point.get("id"));
                dpl.add(p);
                Map pp1 = new HashMap();
                pp1.put("id", point2.get("id"));
                dpl.add(pp1);
                buildObjectsAndRelationsContext.getStraightLineObjects().put(o2, straightLine);

                PointSparnll pointSparnll2 = new PointSparnll();
                pointSparnll2.setName(p1);
                PointSparnll pointSparnll3 = new PointSparnll();
                pointSparnll3.setName(p2);
                pointDTOS.add(pointSparnll2);
                pointDTOS.add(pointSparnll3);

                if (o2.contains("x") && !o2.contains("y")) {
                    Argument x = new Argument(o2);
                    pointSparnll2.setCoord(Arrays.asList(
                        new BigDecimal(x.getArgumentValue()).multiply(scale).add(pointSparnll.getCoord().get(0)),
                        pointSparnll.getCoord().get(1)));
                    pointSparnll3.setCoord(Arrays.asList(
                        new BigDecimal(x.getArgumentValue()).multiply(scale).add(pointSparnll.getCoord().get(0)),
                        new BigDecimal(1).multiply(scale).multiply(new BigDecimal(-1))
                            .add(pointSparnll.getCoord().get(1))));
                } else if (!o2.contains("x") && o2.contains("y")) {
                    Argument y = new Argument(o2);
                    pointSparnll2.setCoord(Arrays.asList(pointSparnll.getCoord().get(0),
                        new BigDecimal(y.getArgumentValue()).multiply(scale).multiply(new BigDecimal(-1))
                            .add(pointSparnll.getCoord().get(1))));
                    pointSparnll3.setCoord(
                        Arrays.asList(new BigDecimal(1).multiply(scale).add(pointSparnll.getCoord().get(0)),
                            new BigDecimal(y.getArgumentValue()).multiply(scale).multiply(new BigDecimal(-1))
                                .add(pointSparnll.getCoord().get(1))));
                } else if (o2.contains("x") && o2.contains("y")) {
                    Argument x = new Argument("x");
                    Argument y = new Argument(o2, x);
                    x.setArgumentValue(0);
                    pointSparnll2.setCoord(Arrays.asList(pointSparnll.getCoord().get(0),
                        new BigDecimal(y.getArgumentValue()).multiply(scale).multiply(new BigDecimal(-1))
                            .add(pointSparnll.getCoord().get(1))));

                    x.setArgumentValue(1);
                    pointSparnll3.setCoord(Arrays.asList(
                        new BigDecimal(x.getArgumentValue()).multiply(scale).add(pointSparnll.getCoord().get(0)),
                        new BigDecimal(y.getArgumentValue()).multiply(scale).multiply(new BigDecimal(-1))
                            .add(pointSparnll.getCoord().get(1))));
                }
            }
        }
        return buildObjectsAndRelationsContext.getStraightLineObjects();
    }

    /**
     * 提取角对象
     *
     * @param rsponseMap
     * @param pointObjects
     * @return
     */
    @NotNull
    private List<Map> extractAngleObjects(Map<String, String> rsponseMap, Map<String, Map> pointObjects) {
        List<Map> angleObjects = new ArrayList<>();
        List s3 = JSONObject.parseObject(rsponseMap.get(ApiKeyGkSceneEnum.ANGLE_OBJECT.getType()), List.class);
        if (s3 == null) {
            s3 = new ArrayList();
        }
        int count3 = 1;
        Set<String> nameSet3 = new HashSet<>();
        for (int i = 0; i < s3.size(); i++) {
            List l = (List) s3.get(i);
            if (l.size() == 1) {
                l.add("");
                l.add("");
            }
            if (l.size() == 2) {
                l.add("");
            }
            // 完整名
            String o = (String) l.get(0);
            // 角度
            String o1 = (String) l.get(1);
            // 简称
            String o2 = (String) l.get(2);

            // 根据名称排除重复
            Boolean isSame = false;
            for (String s : nameSet3) {
                if (isSameAngle(s, o)) {
                    // 优先用三点名称的角，把只有顶点的删了
                    // if (s.length() < o.length()) {
                    //     nameSet3.remove(s);
                    //     angleObjects.remove(s);
                    // } else {
                    isSame = true;
                    // }
                    break;
                }
            }
            if (isSame) {
                continue;
            }
            Map<String, String> angleMap = new HashMap<>();
            Map point = new HashMap();
            point.put("id", "a_" + count3);
            count3++;
            point.put("category", "angle");
            point.put("name", o);
            point.put("type", "normal");
            point.put("value", charFilter(o1));
            angleMap.put("degree", charFilter(o1));
            point.put("values", angleMap);
            point.put("shortName", o2);
            List dpl = new ArrayList<>();
            point.put("determining_points", dpl);
            List<String> points = splitPointNameList(o);
            boolean noPoint = false;
            for (int j = 0; j < points.size(); j++) {
                Map p = new HashMap();
                if (pointObjects.get(points.get(j)) != null) {
                    p.put("id", pointObjects.get(points.get(j)).get("id"));
                    dpl.add(p);
                } else {
                    noPoint = true;
                    log.error("丢弃角 {} 没有点 {} points:{}", o, points.get(j), JSONObject.toJSONString(pointObjects));
                }
            }
            if (noPoint) {
                continue;
            }

            nameSet3.add(o);
            angleObjects.add(point);
        }
        return angleObjects;
    }

    /**
     * 提取三角形对象
     *
     * @param rsponseMap
     * @param pointObjects
     * @return
     */
    @NotNull
    private Map<String, Map> extractTriangleObjects(Map<String, String> rsponseMap, Map<String, Map> pointObjects) {
        Map<String, Map> triangleObjects = new HashMap<>();
        List s4 = JSONObject.parseObject(rsponseMap.get(ApiKeyGkSceneEnum.TRIANGLE_OBJECT.getType()), List.class);
        if (s4 == null) {
            s4 = new ArrayList();
        }
        int count4 = 1;
        Set<String> nameSet4 = new HashSet<>();
        for (int i = 0; i < s4.size(); i++) {
            List l = (List) s4.get(i);
            // 完整名
            String o = (String) l.get(0);
            // 面积
            String o1 = "";
            if (l.size() > 1) {
                o1 = (String) l.get(1);
            }

            // 根据名称排除重复
            Boolean isSame = false;
            for (String s : nameSet4) {
                if (isSamePolygon(s, o)) {
                    isSame = true;
                    break;
                }
            }
            if (isSame) {
                continue;
            }

            Map point = new HashMap();
            Map<String, String> areaMap = new HashMap<>();
            point.put("id", "t_" + count4);
            count4++;
            point.put("category", "triangle");
            point.put("name", o);
            point.put("type", "normal");
            point.put("value", charFilter(o1));
            areaMap.put("area", charFilter(o1));
            point.put("values", areaMap);
            List dpl = new ArrayList<>();
            point.put("determining_points", dpl);
            List<String> points = splitPointNameList(o);
            boolean noPoint = false;
            for (int j = 0; j < points.size(); j++) {
                Map p = new HashMap();
                if (pointObjects.get(points.get(j)) != null) {
                    p.put("id", pointObjects.get(points.get(j)).get("id"));
                    dpl.add(p);
                } else {
                    noPoint = true;
                    log.error("丢弃三角形 {} 没有点 {} points:{}", o, points.get(j),
                        JSONObject.toJSONString(pointObjects));
                }
            }
            if (noPoint) {
                continue;
            }
            nameSet4.add(o);
            triangleObjects.put(o, point);
        }
        return triangleObjects;
    }

    /**
     * 提取四边形对象
     *
     * @param rsponseMap
     * @param pointObjects
     * @return
     */
    @NotNull
    private Map<String, Map> extractQuadrilateralObjects(Map<String, String> rsponseMap,
        Map<String, Map> pointObjects) {
        Map<String, Map> quadrilateralObjects = new HashMap<>();
        List s4 = JSONObject.parseObject(rsponseMap.get(ApiKeyGkSceneEnum.QUADRILATERAL.getType()), List.class);
        if (s4 == null) {
            s4 = new ArrayList();
        }
        int count4 = 1;
        Set<String> nameSet4 = new HashSet<>();
        for (int i = 0; i < s4.size(); i++) {
            List l = (List) s4.get(i);
            // 完整名
            String o = (String) l.get(0);
            // 面积
            String o1 = l.size() > 1 ? (String) l.get(1) : "";
            // 四边形子类型
            // 长方形 / 矩形 → rectangle，四个直角
            // 正方形 → square，四个直角，四边相等
            // trapezoid：梯形 无任何矫正
            // rhombus：菱形 四边相等 对边平行
            // parallelogram：平行四边形  对边平行相等
            // quadrilateral：非矩形、正方形、梯形、菱形、平行四边形的其他四边形 无任何矫正
            String o2 = l.size() > 2 ? (String) l.get(2) : "";

            // 根据名称排除重复
            Boolean isSame = false;
            for (String s : nameSet4) {
                if (isSamePolygon(s, o)) {
                    isSame = true;
                    break;
                }
            }
            if (isSame) {
                continue;
            }
            Map areaMap = new HashMap();
            Map point = new HashMap();
            point.put("id", "q_" + count4);
            count4++;
            point.put("category", "quadrilateral");
            point.put("subCategory", o2);
            point.put("name", o);
            point.put("type", "normal");
            point.put("value", charFilter(o1));
            areaMap.put("area", charFilter(o1));
            point.put("values", areaMap);
            List dpl = new ArrayList<>();
            point.put("determining_points", dpl);
            List<String> points = splitPointNameList(o);
            boolean noPoint = false;
            for (int j = 0; j < points.size(); j++) {
                Map p = new HashMap();
                if (pointObjects.get(points.get(j)) != null) {
                    p.put("id", pointObjects.get(points.get(j)).get("id"));
                    dpl.add(p);
                } else {
                    noPoint = true;
                    log.error("丢弃四边形 {} 没有点 {} points:{}", o, points.get(j),
                        JSONObject.toJSONString(pointObjects));
                }
            }
            if (noPoint) {
                continue;
            }
            nameSet4.add(o);
            quadrilateralObjects.put(o, point);
        }
        return quadrilateralObjects;
    }

    /**
     * 提取多边形对象
     *
     * @param rsponseMap
     * @param pointObjects
     * @return
     */
    @NotNull
    private Map<String, Map> extractPolygonObjects(Map<String, String> rsponseMap, Map<String, Map> pointObjects) {
        Map<String, Map> polygonObjects = new HashMap<>();
        List s4 = JSONObject.parseObject(rsponseMap.get(ApiKeyGkSceneEnum.POLYGON.getType()), List.class);
        if (s4 == null) {
            s4 = new ArrayList();
        }
        int count4 = 1;
        Set<String> nameSet4 = new HashSet<>();
        for (int i = 0; i < s4.size(); i++) {
            List l = (List) s4.get(i);
            // 完整名
            String o = (String) l.get(0);
            // 边数量
            String o1 = (String) l.get(1);
            // 面积
            String o2 = (String) l.get(2);

            // 根据名称排除重复
            Boolean isSame = false;
            for (String s : nameSet4) {
                if (isSamePolygon(s, o)) {
                    isSame = true;
                    break;
                }
            }
            if (isSame) {
                continue;
            }
            Map polygonMap = new HashMap();
            Map point = new HashMap();
            point.put("id", "p_" + count4);
            count4++;
            point.put("category", "polygon");
            point.put("name", o);
            point.put("type", "normal");
            // 边数
            polygonMap.put("sides", charFilter(o1));
            // 面积
            polygonMap.put("area", charFilter(o2));
            point.put("values", polygonMap);
            List dpl = new ArrayList<>();
            point.put("determining_points", dpl);
            List<String> points = splitPointNameList(o);
            boolean noPoint = false;
            for (int j = 0; j < points.size(); j++) {
                Map p = new HashMap();
                if (pointObjects.get(points.get(j)) != null) {
                    p.put("id", pointObjects.get(points.get(j)).get("id"));
                    dpl.add(p);
                } else {
                    noPoint = true;
                    log.error("丢弃多边形 {} 没有点 {} points:{}", o, points.get(j),
                        JSONObject.toJSONString(pointObjects));
                }
            }
            if (noPoint) {
                continue;
            }
            nameSet4.add(o);
            polygonObjects.put(o, point);
        }
        return polygonObjects;
    }

    /**
     * 提取圆弧对象
     *
     * @param rsponseMap
     * @param pointObjects
     * @return
     */
    @NotNull
    private Map<String, Map> extractCircularArcObjects(Map<String, String> rsponseMap, Map<String, Map> pointObjects) {
        Map<String, Map> polygonObjects = new HashMap<>();
        List s4 = JSONObject.parseObject(rsponseMap.get(ApiKeyGkSceneEnum.CIRCULAR_ARC.getType()), List.class);
        if (s4 == null) {
            s4 = new ArrayList();
        }
        int count4 = 1;
        Set<String> nameSet4 = new HashSet<>();
        for (int i = 0; i < s4.size(); i++) {
            List l = (List) s4.get(i);
            if (l.size() < 4) {
                continue;
            }
            // 圆心名称
            String o = (String) l.get(0);
            // 特殊命名
            String o1 = (String) l.get(1);
            // 端点1名称
            String o2 = (String) l.get(2);
            // 端点2名称
            String o3 = (String) l.get(3);

            List<String> al = new ArrayList<>();
            al.add(l.get(0).toString());
            al.addAll(l.subList(2, l.size()));
            String oo = String.join("", al);

            if (al.stream().anyMatch(StringUtils::isBlank) || Objects.equals(o2, o3)) {
                // 漏点或者点不对的丢弃
                log.error("丢弃圆弧 漏点 info:{}", JSONObject.toJSONString(l));
                continue;
            }

            // 根据名称排除重复
            Boolean isSame = false;
            for (String s : nameSet4) {
                if (isSameArc(s, oo)) {
                    isSame = true;
                    break;
                }
            }
            if (isSame) {
                continue;
            }

            Map point = new HashMap();
            point.put("id", "ca_" + count4);
            count4++;
            point.put("category", "arc");
            point.put("name", oo);
            point.put("type", "normal");
            point.put("value", charFilter(o3));
            List dpl = new ArrayList<>();
            List dpl1 = new ArrayList<>();
            point.put("determining_points", dpl);
            point.put("determining_points_name", dpl1);
            boolean noPoint = false;
            for (int j = 0; j < l.size(); j++) {
                if (j == 1) {
                    continue;
                }
                Map p = new HashMap();
                if (pointObjects.get(l.get(j)) != null) {
                    p.put("id", pointObjects.get(l.get(j)).get("id"));
                    dpl.add(p);
                    dpl1.add(l.get(j));
                } else {
                    noPoint = true;
                    log.error("丢弃圆弧 {} 没有点 {} points:{}", oo, l.get(j), JSONObject.toJSONString(pointObjects));
                }
            }
            if (noPoint) {
                continue;
            }
            nameSet4.add(oo);
            polygonObjects.put(oo, point);
        }
        return polygonObjects;
    }

    /**
     * 提取圆规弧对象
     *
     * @param rsponseMap
     * @param pointObjects
     * @return
     */
    @NotNull
    private Map<String, Map> extractCompassArcObjects(Map<String, String> rsponseMap,
        Map<String, Map> pointObjects) {
        Map<String, Map> polygonObjects = new HashMap<>();
        List s4 = JSONObject.parseObject(rsponseMap.get(ApiKeyGkSceneEnum.COMPASS_ARC.getType()), List.class);
        if (s4 == null) {
            s4 = new ArrayList();
        }
        // 将三个点得圆规胡，变成俩个点得圆规弧
        List<List<String>> processedS4 = new ArrayList<>();
        for (Object o : s4) {
            if (o instanceof List) {
                List l = (List) o;
                if (l.size() > 2) {
                    // 第一个点与后面每个点分别组合
                    for (int i = 1; i < l.size(); i++) {
                        processedS4.add(Arrays.asList((String) l.get(0), (String) l.get(i)));
                    }
                } else if (l.size() == 2) {
                    processedS4.add(l);
                }
            }
        }
        s4 = processedS4;
        int count4 = 1;
        Set<String> nameSet4 = new HashSet<>();
        for (int i = 0; i < s4.size(); i++) {
            List l = (List) s4.get(i);
            // 圆规弧的圆心名称
            String o = (String) l.get(0);
            // // 圆规弧的半径长度
            // String o1 = (String) l.get(1);
            // // 圆规弧和其他图形产生的交点名称
            // String o2 = (String) l.get(2);

            // List<String> al = new ArrayList<>();
            // al.add(l.get(0).toString());
            // al.addAll(l.subList(1, l.size()));
            // 圆心名称
            String oo = String.join("", l);

            // 根据名称排除重复
            Boolean isSame = false;
            for (String s : nameSet4) {
                if (isSameArc(s, oo)) {
                    isSame = true;
                    break;
                }
            }
            if (isSame) {
                continue;
            }

            Map point = new HashMap();
            Map CompassArcMap = new HashMap();
            point.put("id", "cas_" + count4);
            count4++;
            point.put("category", "compassArc");
            point.put("name", oo);
            point.put("type", "normal");
            // point.put("value", charFilterConfigService.charFilter(
            //     CharFilterConfigEnum.BOARDNOTE_REGEX_FILTER.getCode(), o1));
            // // 半径长度
            // polygonMap.put("length", charFilterConfigService.charFilter(
            //     CharFilterConfigEnum.BOARDNOTE_REGEX_FILTER.getCode(), o1));
            point.put("values", CompassArcMap);
            List dpl = new ArrayList<>();
            List dpl1 = new ArrayList<>();
            point.put("determining_points", dpl);
            point.put("determining_points_name", dpl1);
            boolean noPoint = false;
            for (int j = 0; j < l.size(); j++) {
                // if (j == 1) {
                //     continue;
                // }
                Map p = new HashMap();
                if (pointObjects.get(l.get(j)) != null) {
                    p.put("id", pointObjects.get(l.get(j)).get("id"));
                    dpl.add(p);
                    dpl1.add(l.get(j));
                } else {
                    noPoint = true;
                    log.error("丢弃圆规弧 {} 没有点 {} points:{}", oo, l.get(j), JSONObject.toJSONString(pointObjects));
                }
            }
            if (noPoint) {
                continue;
            }
            nameSet4.add(oo);
            polygonObjects.put(oo, point);
        }
        return polygonObjects;
    }

    /**
     * 提取圆对象
     *
     * @param rsponseMap
     * @param pointObjects
     * @return
     */
    @NotNull
    private Map<String, Map> extractCircleObjects(Map<String, String> rsponseMap,
        Map<String, Map> pointObjects) {
        Map<String, Map> polygonObjects = new HashMap<>();
        List s4 = JSONObject.parseObject(rsponseMap.get(ApiKeyGkSceneEnum.CIRCLE.getType()), List.class);
        if (s4 == null) {
            s4 = new ArrayList();
        }
        int count4 = 1;
        Set<String> nameSet4 = new HashSet<>();
        for (int i = 0; i < s4.size(); i++) {
            List l = (List) s4.get(i);
            // 圆心名称
            String o = (String) l.get(0);
            // 圆上任意一点名称
            String o1 = (String) l.get(1);

            String oo = String.join("", l);

            // 根据名称排除重复
            Boolean isSame = false;
            for (String s : nameSet4) {
                if (isSamePolygon(s, oo)) {
                    isSame = true;
                    break;
                }
            }
            if (isSame) {
                continue;
            }

            Map point = new HashMap();
            point.put("id", "c_" + count4);
            count4++;
            point.put("category", "circle");
            point.put("name", oo);
            point.put("type", "normal");
            // point.put("value", o1);
            List dpl = new ArrayList<>();
            List dpl1 = new ArrayList<>();
            point.put("determining_points", dpl);
            point.put("determining_points_name", dpl1);
            boolean noPoint = false;
            int pointCount = 0;
            for (int j = 0; j < l.size(); j++) {
                Map p = new HashMap();
                if (pointObjects.get(l.get(j)) != null) {
                    p.put("id", pointObjects.get(l.get(j)).get("id"));
                    dpl.add(p);
                    dpl1.add(l.get(j));
                    pointCount++;
                } else {
                    noPoint = true;
                    log.error("丢弃圆 {} 没有点 {} points:{}", oo, l.get(j), JSONObject.toJSONString(pointObjects));
                }
            }
            if (noPoint && pointCount < 4) {
                continue;
            }
            nameSet4.add(oo);
            polygonObjects.put(oo, point);
        }
        return polygonObjects;
    }


    /**
     * 提取数轴对象
     *
     * @param rsponseMap
     * @param pointObjects
     * @return
     */
    @NotNull
    private Map<String, Map> extractNumberLineObjects(Map<String, String> rsponseMap,
        Map<String, Map> pointObjects) {
        Map<String, Map> polygonObjects = new HashMap<>();

         if (StringUtils.isBlank(rsponseMap.get(ApiKeyGkSceneEnum.NUMBER_LINE.getType()))) {
            return polygonObjects;
        }
        List l = JSONObject.parseObject(JSON.toJSONString(jsonFixedService.parseArray(
            rsponseMap.get(ApiKeyGkSceneEnum.NUMBER_LINE.getType()), "[\"\"]")), List.class);
        if (l == null || l.size() == 0) {
            l = new ArrayList();
            return polygonObjects;
        }
        int count4 = 1;
        Set<String> nameSet4 = new HashSet<>();
        // for (int i = 0; i < s4.size(); i++) {
        //     List l = (List) s4.get(i);
        // 数轴一点名称
        String o = (String) l.get(0);
        // 数轴另一点名称
        String o1 = (String) l.get(1);

        // 根据名称排除重复
        Boolean isSame = false;
        for (String s : nameSet4) {
            if (isSamePolygon(s, o)) {
                isSame = true;
                break;
            }
        }
        if (isSame) {
            // continue;
            return polygonObjects;
        }

        Map point = new HashMap();
        point.put("id", "nl_" + count4);
        count4++;
        point.put("category", "numberLine");
        point.put("name", o);
        point.put("type", "normal");
        // point.put("value", o1);
        List dpl = new ArrayList<>();
        point.put("determining_points", dpl);
        boolean noPoint = false;
        for (int j = 0; j < l.size(); j++) {
            Map p = new HashMap();
            if (pointObjects.get(l.get(j)) != null) {
                p.put("id", pointObjects.get(l.get(j)).get("id"));
                dpl.add(p);
            } else {
                noPoint = true;
                log.error("丢弃数轴 {} 没有点 {} points:{}", o, l.get(j), JSONObject.toJSONString(pointObjects));
            }
        }
        if (noPoint) {
            // continue;
            return polygonObjects;
        }
        nameSet4.add(o);
        polygonObjects.put(o, point);
        // }
        return polygonObjects;
    }

    /**
     * 提取二维坐标系对象
     *
     * @param rsponseMap
     * @param pointObjects
     * @return
     */
    @NotNull
    private Map<String, Map> extractCartesianCoordinateSystemObjects(Map<String, String> rsponseMap,
        Map<String, Map> pointObjects, List<PointDTO> inaccuratePoints) {
        Map<String, Map> polygonObjects = new HashMap<>();
        List l = JSONObject.parseObject(rsponseMap.get(ApiKeyGkSceneEnum.CARTESIAN_COORDINATE_SYSTEM.getType()),
            List.class);
        if (l == null || l.size() == 0) {
            l = new ArrayList();
            return polygonObjects;
        }
        int count4 = 1;
        Set<String> nameSet4 = new HashSet<>();
        // for (int i = 0; i < l.size(); i++) {
        // List l = (List) s4.get(i);
        // 原点名称
        String a = (String) l.get(0);
        if (a.equals("yes")) {
            String o = (String) l.get(1);
            // 根据名称排除重复
            Boolean isSame = false;
            for (String s : nameSet4) {
                if (isSamePolygon(s, o)) {
                    isSame = true;
                    break;
                }
            }
            if (isSame) {
                // continue;
                return polygonObjects;
            }

            Map point = new HashMap();
            point.put("id", "ccs_" + count4);
            count4++;
            point.put("category", "cartesianCoordinateSystem");
            point.put("name", o);
            point.put("type", "normal");
            // point.put("value", o1);
            List dpl = new ArrayList<>();
            point.put("determining_points", dpl);
            List<String> points = splitPointNameList(o);
            boolean noPoint = false;
            for (int j = 0; j < points.size(); j++) {
                Map p = new HashMap();
                if (pointObjects.get(points.get(j)) != null) {
                    p.put("id", pointObjects.get(points.get(j)).get("id"));
                    dpl.add(p);

                    Map oPoint = pointObjects.get(points.get(j));
                    String oName = oPoint.get("name").toString();
                    PointDTO pointSparnll = inaccuratePoints.stream().filter(pp -> pp.getName().equals(oName))
                        .findFirst()
                        .orElse(null);
                    if (pointSparnll == null) {
                        continue;
                    }
                    List<BigDecimal> sl = new ArrayList<>();
                    for (Map aPoint : pointObjects.values()) {
                        if (!aPoint.get("name").equals(oName) && aPoint.get("qx") != null
                            && aPoint.get("qy") != null) {

                            PointDTO pointSparnll1 = inaccuratePoints.stream()
                                .filter(pp -> pp.getName().equals(aPoint.get("name")))
                                .findFirst()
                                .orElse(null);
                            if (pointSparnll1 == null) {
                                continue;
                            }
                            BigDecimal oX = new BigDecimal(pointSparnll.getCoord().get(0));
                            BigDecimal oY = new BigDecimal(pointSparnll.getCoord().get(1));
                            BigDecimal aX = new BigDecimal(pointSparnll1.getCoord().get(0));
                            BigDecimal aY = new BigDecimal(pointSparnll1.getCoord().get(1));
                            // 计算测绘距离
                            double distanceOoAa = Math.sqrt(
                                Math.pow(oX.doubleValue() - aX.doubleValue(), 2) + Math.pow(
                                    oY.doubleValue() - aY.doubleValue(),
                                    2));
                            // 计算题目距离
                            double distanceOoBb = Math.sqrt(
                                Math.pow(new BigDecimal(aPoint.get("qx").toString()).doubleValue(), 2) + Math.pow(
                                    new BigDecimal(aPoint.get("qy").toString()).doubleValue(), 2));
                            if (new BigDecimal(distanceOoBb).compareTo(BigDecimal.ZERO) != 0) {
                                BigDecimal scale = new BigDecimal(distanceOoAa).divide(new BigDecimal(distanceOoBb), 10,
                                    RoundingMode.HALF_UP);
                                sl.add(scale);
                            }
                        }
                    }
                    if (sl.size() > 0) {
                        BigDecimal sum = BigDecimal.ZERO;
                        for (BigDecimal num : sl) {
                            sum = sum.add(num);
                        }
                        BigDecimal averageScale = sum.divide(new BigDecimal(sl.size()), 10, BigDecimal.ROUND_HALF_UP);
                        // 比例尺
                        point.put("scale", averageScale.doubleValue());
                    }
                } else {
                    noPoint = true;
                    log.error("丢弃二维坐标系 {} 没有点 {} points:{}", o, points.get(j),
                        JSONObject.toJSONString(pointObjects));
                }
            }
            if (noPoint) {
                // continue;
                return polygonObjects;
            }
            nameSet4.add(o);
            polygonObjects.put(o, point);
        }
        // }
        return polygonObjects;
    }


    /**
     * 提取反比例双曲线对象
     *
     * @param rsponseMap
     * @param pointObjects
     * @return
     */
    @NotNull
    private Map<String, Map> extractInverseProportionHyperbolaObjects(Map<String, String> rsponseMap,
        Map<String, Map> pointObjects) {
        Map<String, Map> polygonObjects = new HashMap<>();
        List s4 = JSONObject.parseObject(rsponseMap.get(ApiKeyGkSceneEnum.INVERSE_PROPORTION_HYPERBOLA.getType()),
            List.class);
        if (s4 == null || s4.size() == 0) {
            s4 = new ArrayList();
        }
        int count4 = 1;
        Set<String> nameSet4 = new HashSet<>();
        for (int i = 0; i < s4.size(); i++) {
            List l = (List) s4.get(i);
            // 反比例函数的表达式
            String o = (String) l.get(0);

            // 根据名称排除重复
            Boolean isSame = false;
            for (String s : nameSet4) {
                if (isSamePolygon(s, o)) {
                    isSame = true;
                    break;
                }
            }
            if (isSame) {
                continue;
                // return polygonObjects;
            }

            Map point = new HashMap();
            point.put("id", "iph_" + count4);
            count4++;
            point.put("category", "hyperbola");
            point.put("name", o);
            point.put("type", "normal");
            point.put("value", o);
            List dpl = new ArrayList<>();
            point.put("determining_points", dpl);
            boolean noPoint = false;
            for (int j = 1; j < l.size(); j++) {
                Map p = new HashMap();
                if (pointObjects.get(l.get(j)) != null) {
                    p.put("id", pointObjects.get(l.get(j)).get("id"));
                    dpl.add(p);
                } else {
                    noPoint = true;
                    log.error("丢弃反比例双曲线 {} 没有点 {} points:{}", o, l.get(j),
                        JSONObject.toJSONString(pointObjects));
                }
            }
            if (noPoint) {
                continue;
                // return polygonObjects;
            }
            nameSet4.add(o);
            polygonObjects.put(o, point);
        }
        return polygonObjects;
    }


    /**
     * 修复一个字母名称角对象特征点
     *
     * @param angleObjects
     * @param triangleObjects
     * @param pointObjects
     * @param lineObjects
     */
    private static void fixAngleObjectsDeterminingPoints(List<Map> angleObjects,
        Map<String, Map> triangleObjects,
        Map<String, Map> quadrilateralObjects,
        Map<String, Map> polygonObjects,
        Map<String, Map> pointObjects, Map<String, Map> lineObjects,
        List<Map> pointRelations) {
        for (Map entry : angleObjects) {
            String pName = entry.get("name").toString();
            if (pName.length() > 0 && pName.length() < 3) {
                boolean fixByPolygon = false;
                // 通过多边形对象修复角对象特征点
                for (Map polygonObject : polygonObjects.values()) {
                    List<String> polygonPoints = splitPointNameList(polygonObject.get("name").toString());
                    Set nameSet = new HashSet(polygonPoints);
                    if (nameSet.contains(pName)) {
                        int index = polygonPoints.indexOf(pName);
                        List dpl = new ArrayList<>();
                        List<String> points = splitPointNameList(
                            polygonPoints.get((index - 1 + polygonPoints.size()) % polygonPoints.size()) + pName
                                + polygonPoints.get((index + 1 + polygonPoints.size()) % polygonPoints.size()));
                        boolean noPoint = false;
                        for (int j = 0; j < points.size(); j++) {
                            Map p = new HashMap();
                            if (pointObjects.get(points.get(j)) != null) {
                                p.put("id", pointObjects.get(points.get(j)).get("id"));
                                dpl.add(p);
                            } else {
                                noPoint = true;
                                log.error("没有点 {} points:{}", points.get(j), JSONObject.toJSONString(pointObjects));
                            }
                        }
                        if (!noPoint) {
                            fixByPolygon = true;
                            entry.put("determining_points", dpl);
                        }
                    }
                }
                if (fixByPolygon) {
                    continue;
                }

                boolean fixByQuadrilateral = false;
                // 通过四边形对象修复角对象特征点
                for (Map quadrilateralObject : quadrilateralObjects.values()) {
                    List<String> quadrilateralPoints = splitPointNameList(quadrilateralObject.get("name").toString());
                    Set nameSet = new HashSet(quadrilateralPoints);
                    if (nameSet.contains(pName)) {
                        int index = quadrilateralPoints.indexOf(pName);
                        List dpl = new ArrayList<>();
                        List<String> points = splitPointNameList(
                            quadrilateralPoints.get(
                                (index - 1 + quadrilateralPoints.size()) % quadrilateralPoints.size()) + pName
                                + quadrilateralPoints.get(
                                (index + 1 + quadrilateralPoints.size()) % quadrilateralPoints.size()));
                        boolean noPoint = false;
                        for (int j = 0; j < points.size(); j++) {
                            Map p = new HashMap();
                            if (pointObjects.get(points.get(j)) != null) {
                                p.put("id", pointObjects.get(points.get(j)).get("id"));
                                dpl.add(p);
                            } else {
                                noPoint = true;
                                log.error("没有点 {} points:{}", points.get(j), JSONObject.toJSONString(pointObjects));
                            }
                        }
                        if (!noPoint) {
                            fixByQuadrilateral = true;
                            entry.put("determining_points", dpl);
                        }
                    }
                }
                if (fixByQuadrilateral) {
                    continue;
                }

                boolean fixByTriangle = false;
                // 通过三角形对象修复角对象特征点
                for (Map triangleObject : triangleObjects.values()) {
                    List<String> trianglePoints = splitPointNameList(triangleObject.get("name").toString());
                    Set nameSet = new HashSet(trianglePoints);
                    if (nameSet.contains(pName)) {
                        trianglePoints.remove(pName);
                        List dpl = new ArrayList<>();
                        List<String> points = splitPointNameList(trianglePoints.get(0) + pName + trianglePoints.get(1));
                        boolean noPoint = false;
                        for (int j = 0; j < points.size(); j++) {
                            Map p = new HashMap();
                            if (pointObjects.get(points.get(j)) != null) {
                                p.put("id", pointObjects.get(points.get(j)).get("id"));
                                dpl.add(p);
                            } else {
                                noPoint = true;
                                log.error("没有点 {} points:{}", points.get(j), JSONObject.toJSONString(pointObjects));
                            }
                        }
                        if (!noPoint) {
                            fixByTriangle = true;
                            entry.put("determining_points", dpl);
                        }
                    }
                }
                if (fixByTriangle) {
                    continue;
                }

                // 根据顶点线段修复特征点
                List<Map> lineMaps = new ArrayList<>();
                for (Map lineObject : lineObjects.values()) {
                    List<String> linePoints = splitPointNameList(lineObject.get("name").toString());
                    Set nameSet = new HashSet(linePoints);
                    if (nameSet.contains(pName)) {
                        lineMaps.add(lineObject);
                    }
                }
                if (lineMaps.size() == 2) {
                    Map lineMap1 = lineMaps.get(0);
                    Map lineMap2 = lineMaps.get(1);

                    List dpl = new ArrayList<>();
                    List<String> points = splitPointNameList(
                        ((String) lineMap1.get("name")).replace(pName, "") + pName
                            + ((String) lineMap2.get("name")).replace(pName, ""));
                    boolean noPoint = false;
                    for (int j = 0; j < points.size(); j++) {
                        Map p = new HashMap();
                        if (pointObjects.get(points.get(j)) != null) {
                            p.put("id", pointObjects.get(points.get(j)).get("id"));
                            dpl.add(p);
                        } else {
                            noPoint = true;
                            log.error("没有点 {} points:{}", points.get(j), JSONObject.toJSONString(pointObjects));
                        }
                    }
                    if (!noPoint) {
                        entry.put("determining_points", dpl);
                    }
                } else if (lineMaps.size() > 2) {
                    // 处理大于2条线的情况，找出相同顶点且没有共线关系的两条边
                    for (int k = 0; k < lineMaps.size(); k++) {
                        for (int m = k + 1; m < lineMaps.size(); m++) {
                            Map lineMap1 = lineMaps.get(k);
                            Map lineMap2 = lineMaps.get(m);

                            // 获取两条边的非公共点
                            List<String> line1Points = splitPointNameList((String) lineMap1.get("name"));
                            List<String> line2Points = splitPointNameList((String) lineMap2.get("name"));
                            String otherPoint1 =
                                line1Points.get(0).equals(pName) ? line1Points.get(1) : line1Points.get(0);
                            String otherPoint2 =
                                line2Points.get(0).equals(pName) ? line2Points.get(1) : line2Points.get(0);

                            // 获取两条边的点对象id
                            List<String> line1PointIds = new ArrayList<>();
                            List<String> line1PointNames = splitPointNameList((String) lineMap1.get("name"));
                            for (String name : line1PointNames) {
                                Map point = pointObjects.get(name);
                                if (point != null) {
                                    line1PointIds.add(point.get("id").toString());
                                }
                            }
                            List<String> line2PointIds = new ArrayList<>();
                            List<String> line2PointNames = splitPointNameList((String) lineMap2.get("name"));
                            for (String name : line2PointNames) {
                                Map point = pointObjects.get(name);
                                if (point != null) {
                                    line2PointIds.add(point.get("id").toString());
                                }
                            }

                            String pId = pointObjects.get(pName).get("id").toString();
                            // 找出非公共点id
                            String otherPointId1 = line1PointIds.get(0).equals(pId) ?
                                line1PointIds.get(1) : line1PointIds.get(0);
                            String otherPointId2 = line2PointIds.get(0).equals(pId) ?
                                line2PointIds.get(1) : line2PointIds.get(0);

                            // 是否在同一共线关系
                            boolean hasCollinearRelation = false;

                            for (Map relation : pointRelations) {
                                if ("N_points_are_collinear".equals(relation.get("type"))) {
                                    List<String> relationPoints = (List<String>) relation.get("objects");
                                    boolean containsPointId = relationPoints.contains(pId);
                                    boolean containsOtherPointId1 = relationPoints.contains(otherPointId1);
                                    boolean containsOtherPointId2 = relationPoints.contains(otherPointId2);

                                    // 检查三点是否在同一共线关系
                                    if ((containsPointId && containsOtherPointId1 && containsOtherPointId2)
                                        || (containsOtherPointId1 && containsOtherPointId2)) {
                                        hasCollinearRelation = true;
                                        break;
                                    }
                                }
                            }

                            if (!hasCollinearRelation) {
                                List dpl = new ArrayList<>();
                                List<String> points = splitPointNameList(otherPoint1 + pName + otherPoint2);
                                boolean noPoint = false;
                                for (int j = 0; j < points.size(); j++) {
                                    Map p = new HashMap();
                                    if (pointObjects.get(points.get(j)) != null) {
                                        p.put("id", pointObjects.get(points.get(j)).get("id"));
                                        dpl.add(p);
                                    } else {
                                        noPoint = true;
                                        log.error("没有点 {} points:{}", points.get(j),
                                            JSONObject.toJSONString(pointObjects));
                                    }
                                }
                                if (!noPoint) {
                                    entry.put("determining_points", dpl);
                                    break;
                                }
                            }
                        }
                        if (entry.get("determining_points") != null) {
                            break;
                        }
                    }
                }
            }
        }
    }

    /**
     * 修复线段提取，根据三角形、四边形、多边形提取线段，
     *
     * @param lineObjects
     * @param triangleObjects
     * @param quadrilateralObjects
     */
    private static void fixLineSegmentObjects(AtomicInteger idCount, Map<String, Map> pointObjects,
        Map<String, Map> lineObjects,
        List<List<String>> lines,
        List<Map> angleObjects,
        Map<String, Map> triangleObjects,
        Map<String, Map> quadrilateralObjects,
        Map<String, Map> polygonObjects,
        Map<String, Map> circularArcObjects,
        Map<String, Map> circleObjects,
        Map<String, Map> compassArcObjects) {
        Set<String> nameSet2 = new HashSet<>();

        for (Map value : lineObjects.values()) {
            nameSet2.add(value.get("name").toString());
        }
        // 测绘共线关系提取(并且去重)
        for (int i = 0; i < lines.size(); i++) {
            List l = new ArrayList<>(lines).get(i);
            String oo = String.join("", l);
            List<String> lls = getLineFromLineInfo(oo);
            for (String ll : lls) {
                String o = ll;
                // 根据名称排除重复
                Boolean isSame = false;
                for (String s : nameSet2) {
                    if (isSameLine(s, o)) {
                        isSame = true;
                        break;
                    }
                }
                if (isSame) {
                    continue;
                }
                Map point = new HashMap();
                point.put("id", "s_" + idCount.getAndIncrement());
                point.put("category", "segment");
                point.put("name", o);
                point.put("type", "normal");
                List dpl = new ArrayList<>();
                point.put("determining_points", dpl);
                List<String> points = splitPointNameList(o);
                boolean noPoint = false;
                for (int j = 0; j < points.size(); j++) {
                    Map p = new HashMap();
                    if (pointObjects.get(points.get(j)) != null) {
                        p.put("id", pointObjects.get(points.get(j)).get("id"));
                        dpl.add(p);
                    } else {
                        noPoint = true;
                        log.error("丢弃线段 {} 没有点 {} points:{}", o, points.get(j),
                            JSONObject.toJSONString(pointObjects));
                    }
                }
                if (noPoint) {
                    continue;
                }
                nameSet2.add(o);
                lineObjects.put(o, point);
            }
        }
        // 角补边
        for (int i = 0; i < angleObjects.size(); i++) {
            Map m = new ArrayList<>(angleObjects).get(i);
            String oo = m.get("name").toString();
            List<String> lls = getLineFromA(oo);
            for (String ll : lls) {
                String o = ll;
                // 根据名称排除重复
                Boolean isSame = false;
                for (String s : nameSet2) {
                    if (isSameLine(s, o)) {
                        isSame = true;
                        break;
                    }
                }
                if (isSame) {
                    continue;
                }
                Map point = new HashMap();
                point.put("id", "s_" + idCount.getAndIncrement());
                point.put("category", "segment");
                point.put("name", o);
                point.put("type", "normal");
                List dpl = new ArrayList<>();
                point.put("determining_points", dpl);
                List<String> points = splitPointNameList(o);
                boolean noPoint = false;
                for (int j = 0; j < points.size(); j++) {
                    Map p = new HashMap();
                    if (pointObjects.get(points.get(j)) != null) {
                        p.put("id", pointObjects.get(points.get(j)).get("id"));
                        dpl.add(p);
                    } else {
                        noPoint = true;
                        log.error("丢弃线段 {} 没有点 {} points:{}", o, points.get(j),
                            JSONObject.toJSONString(pointObjects));
                    }
                }
                if (noPoint) {
                    continue;
                }
                nameSet2.add(o);
                lineObjects.put(o, point);
            }
        }

        for (int i = 0; i < triangleObjects.values().size(); i++) {
            Map m = new ArrayList<>(triangleObjects.values()).get(i);
            String oo = m.get("name").toString();
            List<String> lls = getLineFromP(oo);
            for (String ll : lls) {
                String o = ll;
                // 根据名称排除重复
                Boolean isSame = false;
                for (String s : nameSet2) {
                    if (isSameLine(s, o)) {
                        isSame = true;
                        break;
                    }
                }
                if (isSame) {
                    continue;
                }
                Map point = new HashMap();
                point.put("id", "s_" + idCount.getAndIncrement());
                point.put("category", "segment");
                point.put("name", o);
                point.put("type", "normal");
                List dpl = new ArrayList<>();
                point.put("determining_points", dpl);
                List<String> points = splitPointNameList(o);
                boolean noPoint = false;
                for (int j = 0; j < points.size(); j++) {
                    Map p = new HashMap();
                    if (pointObjects.get(points.get(j)) != null) {
                        p.put("id", pointObjects.get(points.get(j)).get("id"));
                        dpl.add(p);
                    } else {
                        noPoint = true;
                        log.error("丢弃线段 {} 没有点 {} points:{}", o, points.get(j),
                            JSONObject.toJSONString(pointObjects));
                    }
                }
                if (noPoint) {
                    continue;
                }
                nameSet2.add(o);
                lineObjects.put(o, point);
            }
        }
        for (int i = 0; i < quadrilateralObjects.values().size(); i++) {
            Map m = new ArrayList<>(quadrilateralObjects.values()).get(i);
            String oo = m.get("name").toString();
            List<String> lls = getLineFromP(oo);
            for (String ll : lls) {
                String o = ll;
                // 根据名称排除重复
                Boolean isSame = false;
                for (String s : nameSet2) {
                    if (isSameLine(s, o)) {
                        isSame = true;
                        break;
                    }
                }
                if (isSame) {
                    continue;
                }
                Map point = new HashMap();
                point.put("id", "s_" + idCount.getAndIncrement());
                point.put("category", "segment");
                point.put("name", o);
                point.put("type", "normal");
                List dpl = new ArrayList<>();
                point.put("determining_points", dpl);
                List<String> points = splitPointNameList(o);
                boolean noPoint = false;
                for (int j = 0; j < points.size(); j++) {
                    Map p = new HashMap();
                    if (pointObjects.get(points.get(j)) != null) {
                        p.put("id", pointObjects.get(points.get(j)).get("id"));
                        dpl.add(p);
                    } else {
                        noPoint = true;
                        log.error("丢弃线段 {} 没有点 {} points:{}", o, points.get(j),
                            JSONObject.toJSONString(pointObjects));
                    }
                }
                if (noPoint) {
                    continue;
                }
                nameSet2.add(o);
                lineObjects.put(o, point);
            }
        }
        for (int i = 0; i < polygonObjects.values().size(); i++) {
            Map m = new ArrayList<>(polygonObjects.values()).get(i);
            String oo = m.get("name").toString();
            List<String> lls = getLineFromP(oo);
            for (String ll : lls) {
                String o = ll;
                // 根据名称排除重复
                Boolean isSame = false;
                for (String s : nameSet2) {
                    if (isSameLine(s, o)) {
                        isSame = true;
                        break;
                    }
                }
                if (isSame) {
                    continue;
                }
                Map point = new HashMap();
                point.put("id", "s_" + idCount.getAndIncrement());
                point.put("category", "segment");
                point.put("name", o);
                point.put("type", "normal");
                List dpl = new ArrayList<>();
                point.put("determining_points", dpl);
                List<String> points = splitPointNameList(o);
                boolean noPoint = false;
                for (int j = 0; j < points.size(); j++) {
                    Map p = new HashMap();
                    if (pointObjects.get(points.get(j)) != null) {
                        p.put("id", pointObjects.get(points.get(j)).get("id"));
                        dpl.add(p);
                    } else {
                        noPoint = true;
                        log.error("丢弃线段 {} 没有点 {} points:{}", o, points.get(j),
                            JSONObject.toJSONString(pointObjects));
                    }
                }
                if (noPoint) {
                    continue;
                }
                nameSet2.add(o);
                lineObjects.put(o, point);
            }
        }

        for (int i = 0; i < circleObjects.values().size(); i++) {
            Map m = new ArrayList<>(circleObjects.values()).get(i);
            String oo = String.join("", (List<String>) m.get("determining_points_name"));
            List<String> lls = getLineFromCircleInfo(oo);
            for (String ll : lls) {
                String o = ll;
                // 根据名称排除重复
                Boolean isSame = false;
                for (String s : nameSet2) {
                    if (isSameLine(s, o)) {
                        isSame = true;
                        lineObjects.get(s).put("circle_r", m.get("id"));
                        break;
                    }
                }
                if (isSame) {
                    continue;
                }
                Map point = new HashMap();
                point.put("id", "s_" + idCount.getAndIncrement());
                point.put("category", "segment");
                point.put("name", o);
                point.put("type", "isTemp");
                List dpl = new ArrayList<>();
                point.put("determining_points", dpl);
                point.put("circle_r", m.get("id"));
                List<String> points = splitPointNameList(o);
                boolean noPoint = false;
                for (int j = 0; j < points.size(); j++) {
                    Map p = new HashMap();
                    if (pointObjects.get(points.get(j)) != null) {
                        p.put("id", pointObjects.get(points.get(j)).get("id"));
                        dpl.add(p);
                    } else {
                        noPoint = true;
                        log.error("丢弃线段 {} 没有点 {} points:{}", o, points.get(j),
                            JSONObject.toJSONString(pointObjects));
                    }
                }
                if (noPoint) {
                    continue;
                }
                nameSet2.add(o);
                lineObjects.put(o, point);
            }
        }
        for (int i = 0; i < circularArcObjects.values().size(); i++) {
            Map m = new ArrayList<>(circularArcObjects.values()).get(i);
            String oo = String.join("", (List<String>) m.get("determining_points_name"));
            List<String> lls = getLineFromCircleInfo(oo);
            for (String ll : lls) {
                String o = ll;
                // 根据名称排除重复
                Boolean isSame = false;
                for (String s : nameSet2) {
                    if (isSameLine(s, o)) {
                        isSame = true;
                        lineObjects.get(s).put("circle_arc_r", m.get("id"));
                        break;
                    }
                }
                if (isSame) {
                    continue;
                }
                Map point = new HashMap();
                point.put("id", "s_" + idCount.getAndIncrement());
                point.put("category", "segment");
                point.put("name", o);
                point.put("type", "isTemp");
                List dpl = new ArrayList<>();
                point.put("determining_points", dpl);
                point.put("circle_arc_r", m.get("id"));
                List<String> points = splitPointNameList(o);
                boolean noPoint = false;
                for (int j = 0; j < points.size(); j++) {
                    Map p = new HashMap();
                    if (pointObjects.get(points.get(j)) != null) {
                        p.put("id", pointObjects.get(points.get(j)).get("id"));
                        dpl.add(p);
                    } else {
                        noPoint = true;
                        log.error("丢弃线段 {} 没有点 {} points:{}", o, points.get(j),
                            JSONObject.toJSONString(pointObjects));
                    }
                }
                if (noPoint) {
                    continue;
                }
                nameSet2.add(o);
                lineObjects.put(o, point);
            }
        }
        for (int i = 0; i < compassArcObjects.values().size(); i++) {
            Map m = new ArrayList<>(compassArcObjects.values()).get(i);
            String oo = String.join("", (List<String>) m.get("determining_points_name"));
            List<String> lls = getLineFromCircleInfo(oo);
            for (String ll : lls) {
                String o = ll;
                // 根据名称排除重复
                Boolean isSame = false;
                for (String s : nameSet2) {
                    if (isSameLine(s, o)) {
                        isSame = true;
                        lineObjects.get(s).put("compass_arc_r", m.get("id"));
                        break;
                    }
                }
                if (isSame) {
                    continue;
                }
                Map point = new HashMap();
                point.put("id", "s_" + idCount.getAndIncrement());
                point.put("category", "segment");
                point.put("name", o);
                point.put("type", "isTemp");
                List dpl = new ArrayList<>();
                point.put("determining_points", dpl);
                point.put("compass_arc_r", m.get("id"));
                List<String> points = splitPointNameList(o);
                boolean noPoint = false;
                for (int j = 0; j < points.size(); j++) {
                    Map p = new HashMap();
                    if (pointObjects.get(points.get(j)) != null) {
                        p.put("id", pointObjects.get(points.get(j)).get("id"));
                        dpl.add(p);
                    } else {
                        noPoint = true;
                        log.error("丢弃线段 {} 没有点 {} points:{}", o, points.get(j),
                            JSONObject.toJSONString(pointObjects));
                    }
                }
                if (noPoint) {
                    continue;
                }
                nameSet2.add(o);
                lineObjects.put(o, point);
            }
        }
    }

    /**
     * 提取点类关系
     *
     * @param rsponseMap
     * @param pointObjects
     * @return
     */
    @NotNull
    private List<Map> extractPointRelations(AtomicInteger idCount, Map<String, String> rsponseMap,
        Map<String, Map> pointObjects, List<List<String>> lines, Map<String, List<Set<String>>> determiningPoints) {
        Map<String, String> pointMapping = new HashMap<>() {
            {
                put("overlap", "superposition_dots");
                put("apart", "non_superposition_dots");
                put("collinear", "N_points_are_collinear");
            }
        };
        List<Map> pointReObjects = new ArrayList();
        if (StringUtils.isBlank(rsponseMap.get(ApiKeyGkSceneEnum.POINT_RELATION.getType()))) {
            return pointReObjects;
        }
        List s5 = JSONObject.parseObject(
            rsponseMap.get(ApiKeyGkSceneEnum.POINT_RELATION.getType()).replaceAll("\\\\\\\\\"", ""),
            List.class);
        // List s6 = JSONObject.parseObject(rsponseMap.get(ApiKeyGkSceneEnum.POINT_RELATION_SPEL.getType()).replaceAll("\\\\\\\\\"", ""), List.class);
        // List s5 = JSONObject.parseObject(JSON.toJSONString(jsonFixedComponent.parseAndFixedJsonArray(
        //     rsponseMap.get(ApiKeyGkSceneEnum.POINT_RELATION.getType()),
        //     "[[\"详细的思考过程\"], [\"collinear\",\"A\",\"B\",\"F\"]]")), List.class);
        // List s6 = JSONObject.parseObject(JSON.toJSONString(jsonFixedComponent.parseAndFixedJsonArray(
        //     rsponseMap.get(ApiKeyGkSceneEnum.POINT_RELATION_CIRCULAR.getType()),
        //     "[ [\"collinear\",\"A\",\"B\",\"F\"], [\"collinear\",\"A\",\"B\",\"F\"]]")), List.class);
        if (s5 == null) {
            s5 = new ArrayList();
        }

        String input = rsponseMap.get(ApiKeyGkSceneEnum.POINT_RELATION_SPEL.getType());
        Pattern pattern = Pattern.compile("\"collinear\":\\s*(\\[.*\\])\\s*\\}");
        Matcher matcher = pattern.matcher(input);
        List<String> allCollinearArrays = new ArrayList<>();
        while (matcher.find()) {
            allCollinearArrays.add(matcher.group(1));
        }
        List<List<String>> s6 = new ArrayList<>();
        for (String s : allCollinearArrays) {
            List<List<String>> collinearList = JSONObject.parseObject(s, List.class);
            for (List<String> collinear : collinearList) {
                collinear.add(0, "collinear");
                s6.add(collinear);
            }
        }
        s5.addAll(s6);

        List<String> idList = new ArrayList<>();
        List<String> typeList = new ArrayList<>();
        for (int i = 0; i < s5.size(); i++) {
            // if (i == 0) {
            //     continue;
            // }
            List l = (List) s5.get(i);
            if (l.size() == 0) {
                continue;
            }
            if (l.size() == 1) {
                continue;
            }
            // 关系
            String o = (String) l.get(0);
            // if(o.equals("collinear")){
            //     continue;
            // }
            // 点
            // String o1 = (String) l.get(1);
            // String o2 = (String) l.get(2);
            // String o3 = (String) l.get(3);

            if ("collinear".equals(o)) {
                // 增加共线关系校验逻辑
                boolean shouldSkip = false;
                for (int j = 1; j < l.size(); j++) {
                    String pointName = (String) l.get(j);
                    Map pointObj = pointObjects.get(pointName);
                    if (pointObj != null) {
                        String pointId = (String) pointObj.get("id");
                        if (determiningPoints.containsKey(pointId)) {
                            // 检查共线关系中的所有其他点是否都在同一个几何对象的点集合中
                            for (Set<String> geometricSet : determiningPoints.get(pointId)) {
                                boolean allInSameSet = true;
                                for (int k = 1; k < l.size(); k++) {
                                    if (k != j) {
                                        String otherPointName = (String) l.get(k);
                                        Map otherPointObj = pointObjects.get(otherPointName);
                                        if (otherPointObj == null || !geometricSet.contains(otherPointObj.get("id"))) {
                                            allInSameSet = false;
                                            break;
                                        }
                                    }
                                }
                                if (allInSameSet) {
                                    shouldSkip = true;
                                    break;
                                }
                            }
                            if (shouldSkip) {
                                break;
                            }
                        }
                    }
                }

                if (shouldSkip) {
                    log.error(
                        "Skip collinear relation for points {} as they are determining points of geometric objects {}",
                        l.subList(1, l.size()), JSON.toJSONString(determiningPoints));
                    continue;
                }
            }

            Map point = new HashMap();
            point.put("id", "r_" + idCount.getAndIncrement());
            point.put("type", pointMapping.get(o));
            List dpl = new ArrayList<>();
            point.put("objects", dpl);
            boolean lost = false;
            for (int j = 1; j < l.size(); j++) {
                if (pointObjects.get(l.get(j)) != null) {
                    dpl.add(pointObjects.get(l.get(j)).get("id"));
                } else {
                    lost = true;
                    log.error("丢弃点关系 丢失点 {} pointObjects:{}", l.get(j), JSONObject.toJSONString(pointObjects));
                }
            }
            // 根据名称排除重复
            Boolean isSame = false;
            for (int j = 0; j < idList.size(); j++) {
                String s = idList.get(j);
                if (isSameIdSet(s, String.join("", dpl)) && Objects.equals(typeList.get(j), pointMapping.get(o))) {
                    isSame = true;
                    break;
                }
            }
            if (lost || isSame) {
                continue;
            }
            idList.add(String.join("", dpl));
            typeList.add(pointMapping.get(o));
            pointReObjects.add(point);
        }
        // 根据测绘补充共线关系
        for (int i = 0; (lines != null && i < lines.size()); i++) {
            // 一组点
            List l = lines.get(i);

            Map point = new HashMap();
            point.put("id", "r_" + idCount.getAndIncrement());
            point.put("type", pointMapping.get("collinear"));
            List<String> dpl = new ArrayList<>();
            point.put("objects", dpl);
            boolean lost = false;
            for (int j = 0; j < l.size(); j++) {
                if (pointObjects.get(l.get(j)) != null) {
                    dpl.add((String) pointObjects.get(l.get(j)).get("id"));
                } else {
                    lost = true;
                    log.error("丢弃点关系 丢失点 {} pointObjects:{}", l.get(j), JSONObject.toJSONString(pointObjects));
                }
            }
            // 根据名称排除重复
            Boolean isSame = false;
            for (int j = 0; j < idList.size(); j++) {
                String s = idList.get(j);
                if (isSameIdSet(s, String.join("", dpl)) && Objects.equals(typeList.get(j),
                    pointMapping.get("collinear"))) {
                    isSame = true;
                    break;
                }
            }
            if (lost || isSame) {
                continue;
            }
            idList.add(String.join("", dpl));
            typeList.add(pointMapping.get("collinear"));
            pointReObjects.add(point);
        }
        return pointReObjects;
    }

    /**
     * 提取线类关系
     *
     * @param rsponseMap
     * @param lineObjects
     * @param quadrilateralObjects
     * @return
     */
    @NotNull
    private List<Map> extractLineRelations(AtomicInteger idCount, Map<String, String> rsponseMap,
        Map<String, Map> lineObjects, Map<String, Map> quadrilateralObjects) {
        Map<String, String> lineReMapping = new HashMap<>() {
            {
                put("overlap", "superposition_segments");
                put("equal", "congruent_segments");
                put("collinear", "collinear");
                put("parallel", "parallel_lines");
                put("perpendicular", "mutual_perpendicular_lines");
                put("intersect", "intersecting_lines");
                put("extension", "opposite_rays");
                put("opposite", "reverse_rays");
                put("same_direction", "coincident_rays");
                // put("horizontal", "horizontal_line");
                // put("vertical", "vertical_line");
            }
        };
        List<Map> lineReObjects = new ArrayList();
        List s6 = JSONObject.parseObject(rsponseMap.get(ApiKeyGkSceneEnum.LINE_RELATION.getType()), List.class);
        // List s6 = JSONObject.parseObject(JSON.toJSONString(jsonFixedComponent.parseAndFixedJsonArray(
        //         rsponseMap.get(ApiKeyGkSceneEnum.LINE_RELATION.getType()),
        //         "[ [\"equal\", \"AD\", \"AF\"]]")), List.class);

        if (s6 == null) {
            s6 = new ArrayList();
        }
        List<String> idList = new ArrayList<>();
        List<String> typeList = new ArrayList<>();
        for (int i = 0; i < s6.size(); i++) {
            List l = (List) s6.get(i);
            // 关系
            String o = (String) l.get(0);
            // 线
            // String o1 = (String) l.get(1);
            // String o2 = (String) l.get(2);

            Map point = new HashMap();
            point.put("id", "r_" + idCount.getAndIncrement());
            point.put("type", lineReMapping.get(o));
            List dpl = new ArrayList<>();
            point.put("objects", dpl);
            boolean lost = false;
            for (int j = 1; j < l.size(); j++) {
                boolean lost1 = true;
                for (Map lineObject : lineObjects.values()) {
                    if (isSameLine(lineObject.get("name").toString(), l.get(j).toString())) {
                        dpl.add(lineObject.get("id"));
                        lost1 = false;
                        break;
                    }
                }
                if (lost1) {
                    lost = true;
                    log.error("丢弃线关系 丢失线段 {} lineObjects:{}", l.get(j), JSONObject.toJSONString(lineObjects));
                }
            }
            // 根据名称排除重复
            Boolean isSame = false;
            for (int j = 0; j < idList.size(); j++) {
                String s = idList.get(j);
                if (isSameIdSet(s, String.join("", dpl)) && Objects.equals(typeList.get(j), lineReMapping.get(o))) {
                    isSame = true;
                    break;
                }
            }
            if (lost || isSame) {
                continue;
            }
            idList.add(String.join("", dpl));
            typeList.add(lineReMapping.get(o));
            lineReObjects.add(point);
        }

        // 根据线段圆半径信息补充线段相等关系
        Map<String, List<String>> circle_r = new HashMap();
        for (int i = 0; i < lineObjects.values().size(); i++) {
            Map lineObject = new ArrayList<>(lineObjects.values()).get(i);
            if (lineObject.get("circle_r") != null) {
                circle_r.putIfAbsent(lineObject.get("circle_r").toString(), new ArrayList<>());
                circle_r.get(lineObject.get("circle_r").toString()).add(lineObject.get("id").toString());
            }
        }
        for (int i = 0; i < circle_r.size(); i++) {
            List<String> l = (List<String>) new ArrayList(circle_r.values()).get(i);
            if (l.size() < 2) {
                continue;
            }
            List<List<String>> result = new ArrayList<>();
            if (l.size() == 2) {
                result.add(Arrays.asList(l.get(0), l.get(l.size() - 1)));
            } else {
                for (int ii = 0; ii < l.size() - 1; ii++) {
                    for (int j = ii + 1; j < l.size(); j++) {
                        result.add(Arrays.asList(l.get(ii), l.get(j)));
                    }
                }
            }

            for (List<String> ll : result) {
                Map point = new HashMap();
                point.put("id", "r_" + idCount.getAndIncrement());
                point.put("type", lineReMapping.get("equal"));
                point.put("isTemp", 1);
                List dpl = new ArrayList<>();
                point.put("objects", dpl);
                boolean lost = false;
                for (int j = 0; j < ll.size(); j++) {
                    dpl.add(ll.get(j));
                }
                // 根据名称排除重复
                Boolean isSame = false;
                for (int j = 0; j < idList.size(); j++) {
                    String s = idList.get(j);
                    if (isSameIdSet(s, String.join("", dpl)) && Objects.equals(typeList.get(j),
                        lineReMapping.get("equal"))) {
                        isSame = true;
                        break;
                    }
                }
                if (lost || isSame) {
                    continue;
                }
                idList.add(String.join("", dpl));
                typeList.add(lineReMapping.get("equal"));
                lineReObjects.add(point);
            }
        }
        // 根据圆弧半径信息补充线段相等关系
        Map<String, List<String>> circle_arc_r = new HashMap();
        for (int i = 0; i < lineObjects.values().size(); i++) {
            Map lineObject = new ArrayList<>(lineObjects.values()).get(i);
            if (lineObject.get("circle_arc_r") != null) {
                circle_arc_r.putIfAbsent(lineObject.get("circle_arc_r").toString(), new ArrayList<>());
                circle_arc_r.get(lineObject.get("circle_arc_r").toString()).add(lineObject.get("id").toString());
            }
        }
        for (int i = 0; i < circle_arc_r.size(); i++) {
            List<String> l = (List<String>) new ArrayList(circle_arc_r.values()).get(i);
            List<List<String>> result = new ArrayList<>();
            if (l.size() < 2) {
                continue;
            } else {
                for (int ii = 0; ii < l.size() - 1; ii++) {
                    for (int j = ii + 1; j < l.size(); j++) {
                        result.add(Arrays.asList(l.get(ii), l.get(j)));
                    }
                }
            }

            for (List<String> ll : result) {
                Map point = new HashMap();
                point.put("id", "r_" + idCount.getAndIncrement());
                point.put("type", lineReMapping.get("equal"));
                point.put("isTemp", 1);
                List dpl = new ArrayList<>();
                point.put("objects", dpl);
                boolean lost = false;
                for (int j = 0; j < ll.size(); j++) {
                    dpl.add(ll.get(j));
                }
                // 根据名称排除重复
                Boolean isSame = false;
                for (int j = 0; j < idList.size(); j++) {
                    String s = idList.get(j);
                    if (isSameIdSet(s, String.join("", dpl)) && Objects.equals(typeList.get(j),
                        lineReMapping.get("equal"))) {
                        isSame = true;
                        break;
                    }
                }
                if (lost || isSame) {
                    continue;
                }
                idList.add(String.join("", dpl));
                typeList.add(lineReMapping.get("equal"));
                lineReObjects.add(point);
            }
        }
        // 根据圆规弧半径信息补充线段相等关系
        Map<String, List<String>> compass_arc_r = new HashMap();
        for (int i = 0; i < lineObjects.values().size(); i++) {
            Map lineObject = new ArrayList<>(lineObjects.values()).get(i);
            if (lineObject.get("compass_arc_r") != null) {
                compass_arc_r.putIfAbsent(lineObject.get("compass_arc_r").toString(), new ArrayList<>());
                compass_arc_r.get(lineObject.get("compass_arc_r").toString()).add(lineObject.get("id").toString());
            }
        }
        for (int i = 0; i < compass_arc_r.size(); i++) {
            List<String> l = (List<String>) new ArrayList(compass_arc_r.values()).get(i);
            if (l.size() < 2) {
                continue;
            }
            List<List<String>> result = new ArrayList<>();
            if (l.size() == 2) {
                result.add(Arrays.asList(l.get(0), l.get(l.size() - 1)));
            } else {
                for (int ii = 0; ii < l.size() - 1; ii++) {
                    for (int j = ii + 1; j < l.size(); j++) {
                        result.add(Arrays.asList(l.get(ii), l.get(j)));
                    }
                }
            }

            for (List<String> ll : result) {
                Map point = new HashMap();
                point.put("id", "r_" + idCount.getAndIncrement());
                point.put("type", lineReMapping.get("equal"));
                point.put("isTemp", 1);
                List dpl = new ArrayList<>();
                point.put("objects", dpl);
                boolean lost = false;
                for (int j = 0; j < ll.size(); j++) {
                    dpl.add(ll.get(j));
                }
                // 根据名称排除重复
                Boolean isSame = false;
                for (int j = 0; j < idList.size(); j++) {
                    String s = idList.get(j);
                    if (isSameIdSet(s, String.join("", dpl)) && Objects.equals(typeList.get(j),
                        lineReMapping.get("equal"))) {
                        isSame = true;
                        break;
                    }
                }
                if (lost || isSame) {
                    continue;
                }
                idList.add(String.join("", dpl));
                typeList.add(lineReMapping.get("equal"));
                lineReObjects.add(point);
            }
        }

        // 根据四边形信息补充线段关系
        for (Map quadrilateralObject : quadrilateralObjects.values()) {
            String subCategory = (String) quadrilateralObject.get("subCategory");
            if (subCategory == null) {
                continue;
            }

            String quadrilateralName = (String) quadrilateralObject.get("name");
            List<String> quadrilateralSides = getLineFromP(quadrilateralName);

            // 过滤出存在的线段
            List<String> existingSides = new ArrayList<>();
            for (String side : quadrilateralSides) {
                for (Map lineObject : lineObjects.values()) {
                    if (isSameLine(lineObject.get("name").toString(), side)) {
                        existingSides.add(lineObject.get("id").toString());
                        break;
                    }
                }
            }

            if (existingSides.size() < 4) {
                continue; // 如果四边形的边不完整，跳过
            }

            if ("rectangle".equals(subCategory) || "square".equals(subCategory)) {
                // 长方形和正方形都有四个直角，相邻边垂直
                for (int i = 0; i < existingSides.size(); i++) {
                    int nextIndex = (i + 1) % existingSides.size();

                    Map perpendicularRelation = new HashMap();
                    perpendicularRelation.put("id", "r_" + idCount.getAndIncrement());
                    perpendicularRelation.put("type", lineReMapping.get("perpendicular"));
                    perpendicularRelation.put("isTemp", 1);
                    List dpl = new ArrayList<>();
                    dpl.add(existingSides.get(i));
                    dpl.add(existingSides.get(nextIndex));
                    perpendicularRelation.put("objects", dpl);

                    // 检查是否重复
                    Boolean isSame = false;
                    for (int j = 0; j < idList.size(); j++) {
                        String s = idList.get(j);
                        if (isSameIdSet(s, String.join("", dpl)) && Objects.equals(typeList.get(j),
                            lineReMapping.get("perpendicular"))) {
                            isSame = true;
                            break;
                        }
                    }
                    if (!isSame) {
                        idList.add(String.join("", dpl));
                        typeList.add(lineReMapping.get("perpendicular"));
                        lineReObjects.add(perpendicularRelation);
                    }
                }
            }

            // rhombus：菱形 四边相等 对边平行
            // parallelogram：平行四边形  对边平行相等
            if ("square".equals(subCategory) || "rhombus".equals(subCategory)) {
                // 正方形四边相等，每条边与其他三条边都相等
                for (int i = 0; i < existingSides.size() - 1; i++) {
                    for (int j = i + 1; j < existingSides.size(); j++) {
                        Map equalRelation = new HashMap();
                        equalRelation.put("id", "r_" + idCount.getAndIncrement());
                        equalRelation.put("type", lineReMapping.get("equal"));
                        equalRelation.put("isTemp", 1);
                        List dpl = new ArrayList<>();
                        dpl.add(existingSides.get(i));
                        dpl.add(existingSides.get(j));
                        equalRelation.put("objects", dpl);

                        // 检查是否重复
                        Boolean isSame = false;
                        for (int k = 0; k < idList.size(); k++) {
                            String s = idList.get(k);
                            if (isSameIdSet(s, String.join("", dpl)) && Objects.equals(typeList.get(k),
                                lineReMapping.get("equal"))) {
                                isSame = true;
                                break;
                            }
                        }
                        if (!isSame) {
                            idList.add(String.join("", dpl));
                            typeList.add(lineReMapping.get("equal"));
                            lineReObjects.add(equalRelation);
                        }
                    }
                }
            }
        }

        return lineReObjects;
    }


    /**
     * 提取角类关系
     *
     * @param rsponseMap
     * @param angleObjects
     * @return
     */
    @NotNull
    private List<Map> extractAngleRelations(AtomicInteger idCount, Map<String, String> rsponseMap,
        List<Map> angleObjects) {
        Map<String, String> angleReMapping = new HashMap<>() {
            {
                put("equal", "equal_angles");
                put("complementary", "sum_90_angles");
                put("supplementary", "adjacent_supplementary_angles");
                put("adjacent_supplementary", "adjacent_supplementary_angles");
                put("vertical", "vertically_opposite_angles");
                put("alternate", "interior_alternate_angles");
                put("corresponding", "corresponding_angles");
                put("consecutive", "consecutive_interior_angles");
            }
        };
        List<Map> angleReObjects = new ArrayList();
        List s7 = JSONObject.parseObject(rsponseMap.get(ApiKeyGkSceneEnum.ANGLE_RELATION.getType()), List.class);
        if (s7 == null) {
            s7 = new ArrayList();
        }
        List<String> idList = new ArrayList<>();
        List<String> typeList = new ArrayList<>();
        for (int i = 0; i < s7.size(); i++) {
            List l = (List) s7.get(i);
            // 关系
            String o = (String) l.get(0);
            // 角
            // String o1 = (String) l.get(1);
            // String o2 = (String) l.get(2);

            Map point = new HashMap();
            point.put("id", "r_" + idCount.getAndIncrement());
            point.put("type", angleReMapping.get(o));
            List dpl = new ArrayList<>();
            point.put("objects", dpl);
            boolean lost = false;
            for (int j = 1; j < l.size(); j++) {
                boolean lost1 = true;
                for (Map angleObject : angleObjects) {
                    if (isSameAngle(angleObject.get("name").toString(), l.get(j).toString())) {
                        dpl.add(angleObject.get("id"));
                        lost1 = false;
                        break;
                    }
                }
                if (lost1) {
                    lost = true;
                    log.error("丢弃角关系 丢失角 {} angleObjects:{}", l.get(j), JSONObject.toJSONString(angleObjects));
                }
            }
            // 根据名称排除重复
            Boolean isSame = false;
            for (int j = 0; j < idList.size(); j++) {
                String s = idList.get(j);
                if (isSameIdSet(s, String.join("", dpl)) && Objects.equals(typeList.get(j), angleReMapping.get(o))) {
                    isSame = true;
                    break;
                }
            }
            if (lost || isSame) {
                continue;
            }
            idList.add(String.join("", dpl));
            typeList.add(angleReMapping.get(o));
            angleReObjects.add(point);
        }
        return angleReObjects;
    }

    /**
     * 提取三角形类关系
     *
     * @param rsponseMap
     * @param angleObjects
     * @return
     */
    @NotNull
    private List<Map> extractTriangleRelations(AtomicInteger idCount, Map<String, String> rsponseMap,
        List<Map> angleObjects) {
        Map<String, String> triangleReMapping = new HashMap<>() {
            {
                put("congruent", "congruent_triangles");
                put("similar", "similar_triangles");
            }
        };
        List<Map> angleReObjects = new ArrayList();
        List s7 = JSONObject.parseObject(rsponseMap.get(ApiKeyGkSceneEnum.THREE_ANGLE_RELATION.getType()), List.class);
        if (s7 == null) {
            s7 = new ArrayList();
        }
        List<String> idList = new ArrayList<>();
        List<String> typeList = new ArrayList<>();
        for (int i = 0; i < s7.size(); i++) {
            List l = (List) s7.get(i);
            // 关系
            String o = (String) l.get(0);
            // 角
            // String o1 = (String) l.get(1);
            // String o2 = (String) l.get(2);

            Map point = new HashMap();
            point.put("id", "r_" + idCount.getAndIncrement());
            point.put("type", triangleReMapping.get(o));
            List dpl = new ArrayList<>();
            point.put("objects", dpl);
            boolean lost = false;
            for (int j = 1; j < l.size(); j++) {
                boolean lost1 = true;
                for (Map angleObject : angleObjects) {
                    if (isSamePolygon(angleObject.get("name").toString(), l.get(j).toString())) {
                        dpl.add(angleObject.get("id"));
                        lost1 = false;
                        break;
                    }
                }
                if (lost1) {
                    lost = true;
                    log.error("丢三角形关系 丢失角 {} angleObjects:{}", l.get(j),
                        JSONObject.toJSONString(angleObjects));
                }
            }
            // 根据名称排除重复
            Boolean isSame = false;
            for (int j = 0; j < idList.size(); j++) {
                String s = idList.get(j);
                if (isSameIdSet(s, String.join("", dpl)) && Objects.equals(typeList.get(j), triangleReMapping.get(o))) {
                    isSame = true;
                    break;
                }
            }
            if (lost || isSame) {
                continue;
            }
            idList.add(String.join("", dpl));
            typeList.add(triangleReMapping.get(o));
            angleReObjects.add(point);
        }
        return angleReObjects;
    }

    /**
     * 殊符号
     */
    // static Set<String> excludes = new HashSet<>() {
    //     {
    //         add("'");
    //         add("′");
    //         add("0");
    //         add("1");
    //         add("2");
    //         add("3");
    //         add("4");
    //         add("5");
    //         add("6");
    //         add("7");
    //         add("8");
    //         add("9");
    //     }
    // };
    //
    // public static List<String> splitPointNameList1(String input) {
    //     List<String> result = new ArrayList<>();
    //     int i = 0;
    //     while (i < input.length()) {
    //         char currentChar = input.charAt(i);
    //         if (i + 1 < input.length() && excludes.contains(String.valueOf(input.charAt(i + 1)))) {
    //             // 如果下一个字符是 '，则将当前字符和 ' 组合在一起
    //             result.add(String.valueOf(currentChar) + "'");
    //             // 跳过当前字符和 '
    //             i += 2;
    //         } else {
    //             // 否则，只添加当前字符
    //             result.add(String.valueOf(currentChar));
    //             i += 1;
    //         }
    //     }
    //     return result;
    // }

    /**
     * 角获取边
     *
     * @param input
     * @return
     */
    public static List<String> getLineFromA(String input) {
        List<String> result = new ArrayList<>();
        List<String> l = splitPointNameList(input);
        if (l.size() == 3) {
            result.add(l.get(1) + l.get(0));
            result.add(l.get(1) + l.get(2));
        }
        return result;
    }

    /**
     * 三角形，多边形 获取边
     *
     * @param input
     * @return
     */
    public static List<String> getLineFromP(String input) {
        List<String> result = new ArrayList<>();
        List<String> l = splitPointNameList(input);
        for (int i = 0; i < l.size() - 1; i++) {
            result.add(l.get(i) + l.get(i + 1));
        }
        if (l.size() > 2) {
            result.add(l.get(0) + l.get(l.size() - 1));
        }
        return result;
    }

    /**
     * 共线场景 排列组合获取所有边
     *
     * @param input
     * @return
     */
    public static List<String> getLineFromLineInfo(String input) {
        List<String> result = new ArrayList<>();
        List<String> l = splitPointNameList(input);
        if (l.size() == 2) {
            result.add(l.get(0) + l.get(l.size() - 1));
        } else {
            for (int i = 0; i < l.size() - 1; i++) {
                for (int j = i + 1; j < l.size(); j++) {
                    result.add(l.get(i) + l.get(j));
                }
            }
        }
        return result;
    }

    /**
     * 根据圆心获取半径线段
     *
     * @param input
     * @return
     */
    public static List<String> getLineFromCircleInfo(String input) {
        List<String> result = new ArrayList<>();
        List<String> l = splitPointNameList(input);
        for (int i = 1; i < l.size(); i++) {
            result.add(l.get(0) + l.get(i));

        }
        return result;
    }

    public static List<String> splitPointNameList(String input) {
        Set<String> result = new LinkedHashSet<>();
        StringBuilder currentElement = new StringBuilder();

        for (char c : input.toCharArray()) {
            if (c >= 'A' && c <= 'Z') {
                if (currentElement.length() > 0) {
                    result.add(currentElement.toString());
                }
                currentElement = new StringBuilder();
                currentElement.append(c);
            } else {
                currentElement.append(c);
            }
        }

        // Add the last element if there is any
        if (currentElement.length() > 0) {
            result.add(currentElement.toString());
        }

        return new ArrayList<>(result);
    }

    /**
     * 带特殊符号反转字符串
     *
     * @param str
     * @return
     */
    private static String reverseString(String str) {
        if (str == null) {
            return null;
        }
        List<String> l = splitPointNameList(str);
        Collections.reverse(l);
        return String.join("", l);
    }

    /**
     * 按首字母排序
     *
     * @param input
     * @return
     */
    public static String orderString(String input) {
        List<String> result = splitPointNameList(input);
        Collections.sort(result, new Comparator<String>() {
            @Override
            public int compare(String s1, String s2) {
                if (s1 == null && s2 == null) {
                    return 0;
                }
                if (s1 == null) {
                    return -1;
                }
                if (s2 == null) {
                    return 1;
                }
                return s1.compareToIgnoreCase(s2);
            }
        });
        return String.join("", result);
    }

    /**
     * 根据名字判断是否是同一条线
     *
     * @param lineName1
     * @param lineName2
     * @return
     */
    private static Boolean isSameLine(String lineName1, String lineName2) {
        return lineName1.equals(lineName2) || lineName1.equals(reverseString(lineName2));
    }

    /**
     * 是否是同一条射线
     *
     * @param lineName1
     * @param lineName2
     * @return
     */
    private Boolean isSameRayLine(String lineName1, String lineName2) {
        return lineName1.equals(lineName2);
    }

    /**
     * 根据名字判断是否是同一条角
     *
     * @param angleName1
     * @param angleName2
     * @return
     */
    private Boolean isSameAngle(String angleName1, String angleName2) {
        if (StringUtils.isEmpty(angleName1) || StringUtils.isEmpty(angleName2)) {
            return false;
        }
        return angleName1.equals(angleName2) || angleName1.equals(reverseString(angleName2));
        // if (angleName1.length() >= 3 && angleName2.length() >= 3) {
        // } else {
        //     // 如果有只有一个顶点的角，根据中间点判断
        //     List<String> angle1List = splitPointNameList(angleName1);
        //     List<String> angle2List = splitPointNameList(angleName2);
        //     return Objects.equals(angle1List.get(angle1List.size() / 2), angle2List.get(angle2List.size() / 2));
        // }
    }

    private Boolean isSameArc(String angleName1, String angleName2) {
        if (StringUtils.isEmpty(angleName1) || StringUtils.isEmpty(angleName2)) {
            return false;
        }

        List<String> list1 = splitPointNameList(angleName1);
        List<String> list2 = splitPointNameList(angleName2);

        if (list1.size() > 1 && list2.size() > 1) {
            // 先比较第一个点
            if (!Objects.equals(list1.get(0), list2.get(0))) {
                return false;
            }
            // 对剩余点排序后比较
            String subList1 = orderString(String.join("", list1.subList(1, list1.size())));
            String subList2 = orderString(String.join("", list2.subList(1, list2.size())));
            return Objects.equals(subList1, subList2);
        }

        return angleName1.equals(angleName2);
    }


    /**
     * 根据斜率判断角边点方向
     *
     * @param xA
     * @param yA
     * @param xB
     * @param yB
     * @param xC
     * @param yC
     * @return
     */
    public static boolean isSameDirection(double xA, double yA, double xB, double yB, double xC, double yC) {
        // 计算向量AB和向量AC
        double ABx = xB - xA;
        double ABy = yB - yA;
        double ACx = xC - xA;
        double ACy = yC - yA;

        // 计算向量AC在向量AB上的投影系数
        double dotProduct = ABx * ACx + ABy * ACy;
        double magnitudeAB = Math.sqrt(ABx * ABx + ABy * ABy);

        // 如果投影系数为正数，说明向量AC与向量AB同方向
        return dotProduct > 0 && magnitudeAB != 0;
    }

    /**
     * 根据名字判断是否是同一个多边形 支持三角形 四边形 多边形判断
     *
     * @param polygonName1
     * @param polygonName2
     * @return
     */
    private static Boolean isSamePolygon(String polygonName1, String polygonName2) {
        // 排序后判断
        return Objects.equals(orderString(polygonName1), orderString(polygonName2));
    }

    private static Boolean isSameIdSet(String polygonName1, String polygonName2) {
        // 排序后判断
        return Objects.equals(orderString(polygonName1.toUpperCase()), orderString(polygonName2.toUpperCase()));
    }

    public String charFilter(String content) {
        content = content.replaceAll("√(.*)", "⢪\\\\sqrt{$1}⢫");
        LinkedHashMap<String, String> replaceMap = new LinkedHashMap<>();

        // 将字符串中 $$...$$ 中的 % 替换为 \%，但保留已转义的 \%
        content = content.replaceAll("(?<!\\\\)%(?=(?:(?!\\$\\$).)*\\$\\$)", "\\\\%");

        replaceMap.put("\\\\\\((.*?)\\\\\\)", "⢪$1⢫");
        replaceMap.put("\\$\\$(.*?)\\\\\\)", "⢪$1⢫");
        replaceMap.put("\\\\\\((.*?)\\$\\$", "⢪$1⢫");
        replaceMap.put("\\$\\$(.*?)\\$\\$", "⢪$1⢫");
        replaceMap.put("(\\$\\$(?:[^\\$]|(?<!\\$)\\$)*?)%(.*?\\$\\$)", "$1\\\\%$2");
        replaceMap.put("\\(\\$(.*?)\\$\\)", "⢪$1⢫");
        replaceMap.put("\\$(.*?)\\$", "⢪$1⢫");

        String result = optimizedMatcherReplacement(content, replaceMap);
        return result;
        // return latexFixedService.fixedText(content);
    }

    /**
     * 按照规则集 rules 依次对输入字符串 input 进行多次替换，返回最终替换后的字符串
     *
     * @param input
     * @param rules
     * @return
     */
    public static String optimizedMatcherReplacement(String input, LinkedHashMap<String, String> rules) {
        if (input == null || input.isEmpty() || input.length() > 5000 || rules == null || rules.isEmpty()) {
            log.warn("optimizedMatcherReplacementUsingReplaceAll Input is null/empty or rules are empty，input:{}",
                input);
            return input;
        }

        // 缓存编译后的 Pattern
        Map<Pattern, String> compiledRules = new LinkedHashMap<>();
        for (Map.Entry<String, String> entry : rules.entrySet()) {
            try {
                String replacement = entry.getValue() == null ? "" : entry.getValue();
                compiledRules.put(Pattern.compile(entry.getKey(), Pattern.DOTALL), replacement);
            } catch (PatternSyntaxException e) {
                log.error("optimizedMatcherReplacement Invalid regex in rules. Regex: {}, Error: {}", entry.getKey(),
                    e.getMessage());
                // 如果有错误正则直接返回原始输入，避免影响后续
                return input;
            }
        }

        // 仅使用一个 StringBuilder 避免重复创建
        StringBuilder result = new StringBuilder(input);
        for (Map.Entry<Pattern, String> entry : compiledRules.entrySet()) {
            Matcher matcher = entry.getKey().matcher(result);
            StringBuilder buffer = new StringBuilder();
            while (matcher.find()) {
                matcher.appendReplacement(buffer, entry.getValue());
            }
            matcher.appendTail(buffer);
            // 替换结果直接更新到 result
            result.setLength(0);
            result.append(buffer);
        }
        return result.toString();
    }

    public static void main(String[] args) {
        String input = "A′D_1E'";
        List<String> result = splitPointNameList(input);
        // System.out.println(result); // 输出: [A, B, B', C, C]
        // System.out.println(JSON.toJSONString(getLineFromP1("ABC"))); // 输出: [A, B, B', C, C]
        System.out.println(isSameDirection(0, 0, 1, 0, 3, -100)); // 输出: [A, B, B', C, C]

        // JAVA: import org.mariuszgromada.math.mxparser.*;
// C#: using org.mariuszgromada.math.mxparser;
// ...
        Argument x = new Argument("x");
        Argument y = new Argument("y = x^2", x);

        x.setArgumentValue(1);
        mXparser.consolePrintln("Res 1: " + y.getArgumentName() + " = " + y.getArgumentValue());

        x.setArgumentValue(2);
        mXparser.consolePrintln("Res 2: " + y.getArgumentName() + " = " + y.getArgumentValue());

        x.setArgumentValue(3);
        mXparser.consolePrintln("Res 3: " + y.getArgumentName() + " = " + y.getArgumentValue());

        Expression e = new Expression("x*y", x, y);
        mXparser.consolePrintln("Res 4: " + e.getExpressionString() + " = " + e.calculate());

        String s = "10．如图，直线 y=（1）/（2）x+2y=\\frac{1}{2} x+2 与 yy 轴交于点 AA ，与直线 y=-（1）/（2）xy=-\\frac{1}{2} x 交于点 BB ，以 ABA B 为边向右作菱形 ABCDA B C D ，点 CC 恰与原点 0 重合，抛物线 y=(x-h)^（2）+ky=(x-h)^{2}+k 的顶点在直线 y=-（1）/（2）xy=-\\frac{1}{2} x 上移动．若拋物线与菱形的边 AB,BCA B, ~ B C 都有公共点，则 hh 的取值范围是()A．-2 ＜= h ＜= （1）/（2）-2 \\leqslant \\mathrm{~h} \\leqslant \\frac{1}{2}B．-2 ＜= h ＜= 1-2 \\leqslant h \\leqslant 1C．-1 ＜= h ＜= （3）/（2）-1 \\leqslant h \\leqslant \\frac{3}{2}D．-1 ＜= h ＜= （1）/（2）-1 \\leqslant \\mathrm{~h} \\leqslant \\frac{1}{2}null【分析】将 y=（1）/（2）x+2y=\\frac{1}{2} x+2 与 y=-（1）/（2）xy=-\\frac{1}{2} x 联立可求得点 BB 的坐标，然后由抛物线的顶点在直线 yy =-（1）/（2）x=-\\frac{1}{2} x 可求得 k=-（1）/（2）hk=-\\frac{1}{2} h ，于是可得到抛物线的解析式为 y=(x-h)^（2）-（1）/（2）hy=(x-h)^{2}-\\frac{1}{2} h ，由图形可知当拋物线经讨点 BB 和点 CC 时抛物线与菱形的边 AB,BCA B, ~ B C 均有交点，然后将点 CC 和点 BB 的坐标代入扡物线的解析式可求得 hh 的值，从而可判断出 hh 的取值范围．【解答】解：:'\\because 将 y=（1）/（2）x+2y=\\frac{1}{2} x+2 与 y=-（1）/（2）xy=-\\frac{1}{2} x 联立得：{[y=（1）/（2）x+2],[y=（1）/（2）x]:}\\left\\{\\begin{array}{l}y=\\frac{1}{2} x+2 \\\\ y=\\frac{1}{2} x\\end{array}\\right. ，解得：{[x=-2],[y=1]:}\\left\\{\\begin{array}{l}x=-2 \\\\ y=1\\end{array}\\right. ．:.\\therefore 点 BB 的坐标为 (-2,1)(-2,1) ．由拋物线的解析式可知掤物线的顶点坐标为( h,k\\mathrm{h}, \\mathrm{k} )．:'\\because 将 x=h,y=kx=h, y=k ，代入得 y=-（1）/（2）xy=-\\frac{1}{2} x 得：-（1）/（2）h=k-\\frac{1}{2} h=k ，解得 k=-（1）/（2）hk=-\\frac{1}{2} h ，:.\\therefore 拋物线的解析式为 y=(x-h)^（2）-（1）/（2）h\\mathrm{y}=(\\mathrm{x}-\\mathrm{h})^{2}-\\frac{1}{2} \\mathrm{~h} ．如图 1 所示：当抛物线经过点 C 时． 将 C(0,0)C(0,0) 代入 y=(x-h)^（2）-（1）/（2）hy=(x-h)^{2}-\\frac{1}{2} h 得：h^（2）-（1）/（2）h=0h^{2}-\\frac{1}{2} h=0 ，解得：h_（1）=0h_{1}=0(舍去)，h_（2）=（1）/（2）h_{2}=\\frac{1}{2} ．如图 2 所示：当拋物线经过点 BB 时．将 B(-2,1)B(-2,1) 代入 y=(x-h)^（2）-（1）/（2）hy=(x-h)^{2}-\\frac{1}{2} h 得：(-2-h)^（2）-（1）/（2）h=1(-2-h)^{2}-\\frac{1}{2} h=1 ，整理得： 2h^（2）+7h+6=02 h^{2}+7 h+6=0 ，解得：h_（1）=-2,h_（2）=-（3）/（2）h_{1}=-2, h_{2}=-\\frac{3}{2}(舍去)．综上所述，hh 的范围是 -2 ＜= h ＜= （1）/（2）-2 \\leqslant h \\leqslant \\frac{1}{2} ．故选：A．\n";
        System.err.println(s.replace("\n", ""));

        String inpu1t = """
            [
            {
            "subject": "OD",
            "object": "⊙O",
            "point": "C",
            "lines": "OD",
            "combinations": [["O", "D", "C"]],
            "collinear": [["O", "D", "C"]]
            },
            {
            "subject": "OD",
            "object": "⊙O",
            "point": "C",
            "lines": "OD",
            "combinations": [["O", "D", "C"]],
            "collinear": [["O", "D", "C"]]
            }
            ]""";
        Pattern pattern = Pattern.compile("\"collinear\":\\s*(\\[.*\\])\\s*\\}");
        Matcher matcher = pattern.matcher(inpu1t);
        List<String> allCollinearArrays = new ArrayList<>();
        while (matcher.find()) {
            allCollinearArrays.add(matcher.group(1));
        }
    }

}
