package com.jzx.aiagents.service.components.graphickits.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.jzx.aiagents.api.components.graphickits.dto.GkTaskDTO;
import com.jzx.aiagents.api.components.graphickits.req.GetGKAccessControllerReq;
import com.jzx.aiagents.api.components.graphickits.req.GraphicKitsReq;
import com.jzx.aiagents.api.model.enums.graphickits.ApiKeyGkSceneEnum;
import com.jzx.aiagents.dao.api.GraphicKitsDao;
import com.jzx.aiagents.dao.model.po.GraphicKitsRecordPO;
import com.jzx.aiagents.service.components.dify.api.DifyService;
import com.jzx.aiagents.service.components.dify.helper.DifyServiceHelper;
import com.jzx.aiagents.service.components.graphickits.BuildObjectAndRelationService;
import com.jzx.aiagents.service.components.graphickits.CorrectCoordinateService;
import com.jzx.aiagents.service.components.graphickits.GraphicKitsAnalysisService;
import com.jzx.aiagents.service.components.graphickits.api.GraphicKitsService;
import com.jzx.aiagents.service.components.graphickits.dto.BuildObjectsAndRelationsContext;
import com.jzx.aiagents.service.components.graphickits.dto.PointDTO;
import com.jzx.aiagents.service.components.graphickits.dto.PointSparnll;
import com.jzx.aiagents.service.components.graphickits.util.MathObjesPointOverLapUtil;
import com.jzx.aiagents.service.components.tools.JsonFixedService;
import com.jzx.common.burial.util.BurialUtil;
import com.jzx.common.lang.annotation.AutoLog;
import com.jzx.common.lang.exception.BizException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @Date 2025/2/7 20:04
 */

@Slf4j
@Service
public class GraphicKitsAnalysisServiceImpl implements GraphicKitsAnalysisService {

    @Autowired
    private GraphicKitsService graphickitsService;

    @Autowired
    private CorrectCoordinateService correctCoordinateService;
    @Autowired
    private BuildObjectAndRelationService buildObjectAndRelationService;

    @Autowired
    private GraphicKitsDao aprGraphicKitsDao;

    @Autowired
    protected DifyServiceHelper difyServiceHelper;

    @Autowired
    protected DifyService difyService;

    @Autowired
    @Qualifier("gkTaskExecutor")
    private ThreadPoolTaskExecutor gkTaskExecutor;

    @Autowired
    private JsonFixedService jsonFixedService;

    /**
     * 仅去掉大写字母和单引号 反引号之间的空格（前后都是目标字符时才去空格）
     * @param input 原始文本（允许null，返回null）
     * @return 处理后的文本
     */
    public static String trimSpacesBetweenUppercaseAndQuote(String input) {
        if (input == null) {
            return null;
        }

        // 正则逻辑：匹配前后都是大写字母或单引号的空格（一个或多个）
        // (?<=[A-Z']) 正向回顾后发断言：确保空格前面是大写字母或单引号
        // (?=[A-Z']) 正向展望先行断言：确保空格后面是大写字母或单引号
        String regex = "(?<=[A-Z'`])\\s+(?=[A-Z'`])";
        return input.replaceAll(regex, "");
    }

    /**
     * getAprGraphicKits整体逻辑
     *
     * @param graphicKitsReq
     * @return
     */
    @Override
    public GkTaskDTO startGkTask(GraphicKitsReq graphicKitsReq) {
        String gkId = IdWorker.getIdStr();
        log.info("startGkTask graphicKitsReq: start{} gkId {}",
            JSONObject.toJSONString(graphicKitsReq), gkId);
        Assert.isTrue(StringUtils.isNotBlank(graphicKitsReq.getTerm()), "题干信息不可为空");
        // Assert.isTrue(StringUtils.isNotBlank(graphicKitsReq.getMethod()), "解析信息不可为空");
        // Assert.isTrue(StringUtils.isNotBlank(graphicKitsReq.getImageUrl()), "获取题目图片失败");
        Assert.isTrue(StringUtils.isNotBlank(graphicKitsReq.getOcrImageUrl()), "几何图不可为空");

        // 清理字母前后空格
        graphicKitsReq.setTerm(trimSpacesBetweenUppercaseAndQuote(graphicKitsReq.getTerm()));
        graphicKitsReq.setMethod(trimSpacesBetweenUppercaseAndQuote(graphicKitsReq.getMethod()));
        graphicKitsReq.setAnswer(trimSpacesBetweenUppercaseAndQuote(graphicKitsReq.getAnswer()));

        String coordinateInfoData = null;
        try {
            // 获取测绘信息
            long getCoordinateTime = System.currentTimeMillis();
            coordinateInfoData = correctCoordinateService.getCoordinate(graphicKitsReq.getOcrImageUrl());
            if (StringUtils.isBlank(coordinateInfoData)) {
                log.error(
                    "doGkTask getCoordinate failed, data is null, gkId {} graphicKitsReq.getOcrImageUrl:{},coordinateInfoData:{}",
                    gkId, graphicKitsReq.getOcrImageUrl(), coordinateInfoData);
                throw new BizException("getCoordinate 获取测绘坐标失败");
            }
            log.info("doGkTask getCoordinateTime:{} gkId {} coordinateInfoData {}",
                (System.currentTimeMillis() - getCoordinateTime) / 1000, gkId, coordinateInfoData);
        } catch (BizException e) {
            log.error(
                "doGkTask getCoordinate failed, data is null, gkId {} graphicKitsReq.getOcrImageUrl:{},coordinateInfoData:{}",
                gkId, graphicKitsReq.getOcrImageUrl(), coordinateInfoData, e);
            throw new BizException("getCoordinate 获取测绘坐标失败" + e.getMessage());
        }

        String inaccuratePoints = JSON.toJSONString(
            JSON.parseObject(coordinateInfoData, Map.class).get("letter_points"));
        String letterPositions = JSON.toJSONString(
            JSON.parseObject(coordinateInfoData, Map.class).get("letter_positions"));

        // 获取测绘点坐标和点对应字母名称坐标
        // Integer pointNum = null;
        Set<String> s =null;
        List<PointDTO> pList = JSON.parseArray(inaccuratePoints, PointDTO.class);
        List<PointDTO> lList = JSON.parseArray(letterPositions, PointDTO.class);
        // 一个点坐标都没有拦截异常场景
        if (pList.isEmpty() && lList.isEmpty()) {
            log.error(
                "doGkTask getCoordinate 异常 一个点坐标都没有 gkId {} getOcrImageUrl{} getImageUrl{} inaccuratePoints:{}",
                gkId, graphicKitsReq.getOcrImageUrl(), graphicKitsReq.getImageUrl(), inaccuratePoints);
            throw new BizException("测绘返回一个点坐标都没有" + " " + gkId);
        } else {
            s = new HashSet<>();
            s.addAll(pList.stream().map(PointDTO::getName).toList());
            s.addAll(lList.stream().map(PointDTO::getName).toList());

        }

        GetGKAccessControllerReq getGkAccessControllerReq = new GetGKAccessControllerReq();
        BeanUtils.copyProperties(graphicKitsReq, getGkAccessControllerReq);
        getGkAccessControllerReq.setPoints(new ArrayList<>(s));
        getGkAccessControllerReq.setBizCode("GK准入器");
        getGkAccessControllerReq.setBizId(gkId);
        getGkAccessControllerReq.setGkId(gkId);
        getGkAccessControllerReq.setTerm(graphicKitsReq.getTerm());
        getGkAccessControllerReq.setMethod(graphicKitsReq.getMethod());
        getGkAccessControllerReq.setAnswer(graphicKitsReq.getAnswer());
        // 调用准入器
        String isPass = graphickitsService.getGKAccessController(getGkAccessControllerReq);
        GkTaskDTO GKTaskDTO = new GkTaskDTO();
        GKTaskDTO.setGkId(gkId);
        if (Objects.equals(isPass, "1")) {
            saveAprGraphicKit(graphicKitsReq, null, null, null,
                null,
                null,
                null,
                gkId, 0);
            // 异步线程处理
            String finalCoordinateInfoData = coordinateInfoData;
            gkTaskExecutor.execute(() -> doGkTask(gkId, finalCoordinateInfoData, graphicKitsReq));
            GKTaskDTO.setGkPass(true);
        } else {
            log.info("doGkTask GK准入判断未通过 gkId {} {}", gkId, JSONObject.toJSONString(graphicKitsReq));
            GKTaskDTO.setGkPass(false);
        }
        return GKTaskDTO;
    }

    @AutoLog
    public String doGkTask(String gkId, String coordinateInfoData, GraphicKitsReq graphicKitsReq) {
        // 调用大模型获取几何对象＆几何关系原始结果
        long llmTime = System.currentTimeMillis();
        // 并发获取模型结果
        ConcurrentHashMap<String, String> llmResponseMap = getAllGkInfoDTOs(gkId, graphicKitsReq);
        log.info("doGkTask llm 型调用总时长 gkId {} llmTime:{}", gkId, (System.currentTimeMillis() - llmTime) / 1000);

        LocalDateTime startTime = LocalDateTime.now();
        try {

            String inaccuratePoints = JSON.toJSONString(
                JSON.parseObject(coordinateInfoData, Map.class).get("letter_points"));
            String letterPositions = JSON.toJSONString(
                JSON.parseObject(coordinateInfoData, Map.class).get("letter_positions"));
            String lines = JSON.toJSONString(JSON.parseObject(coordinateInfoData, Map.class).get("lines"));

            // 获取测绘点坐标和点对应字母名称坐标
            List<PointDTO> pList = JSON.parseArray(inaccuratePoints, PointDTO.class);
            List<PointDTO> lList = JSON.parseArray(letterPositions, PointDTO.class);
            // 一个点坐标都没有拦截异常场景
            if (pList.isEmpty() && lList.isEmpty()) {
                log.error(
                    "doGkTask getCoordinate 异常 一个点坐标都没有 gkId {} getOcrImageUrl{} getImageUrl{} inaccuratePoints:{}",
                    gkId, graphicKitsReq.getOcrImageUrl(), graphicKitsReq.getImageUrl(), inaccuratePoints);
                throw new BizException("测绘返回一个点坐标都没有" + " " + gkId);
            }

            List s = JSONObject.parseObject(llmResponseMap.get(ApiKeyGkSceneEnum.POINT_OBJECT.getType()), List.class);
            if (CollectionUtils.isEmpty(s)) {
                log.error(
                    "doGkTask 大模型解析返回 一个点坐标都没有 gkId {} getOcrImageUrl{} getImageUrl{} llmInfo{}",
                    gkId, graphicKitsReq.getOcrImageUrl(), graphicKitsReq.getImageUrl(),
                    llmResponseMap.get(ApiKeyGkSceneEnum.POINT_OBJECT.getType()));
                throw new BizException("大模型解析返回一个点坐标都没有" + " " + gkId);
            }
            // 处理只有字母坐标没有点坐标点场景，用字母坐标替代点坐标，字母坐标右上移动50
            Set<String> set = new HashSet<>();
            Map<String, PointDTO> setP = new HashMap<>();
            for (int i = 0; i < pList.size(); i++) {
                PointDTO map = pList.get(i);
                set.add(map.getName());
                setP.put(map.getName(), map);
            }
            for (int i = 0; i < lList.size(); i++) {
                PointDTO map = lList.get(i);
                if (!set.contains(map.getName())) {
                    PointDTO map1 = JSON.parseObject(JSON.toJSONString(map), PointDTO.class);
                    pList.add(map1);
                    map.getCoord().set(0, map.getCoord().get(0) - 50);
                    map.getCoord().set(1, map.getCoord().get(1) - 50);
                } else {
                    PointDTO map1 = setP.get(map.getName());
                    // 如果坐标差值小于5，则认为坐标重叠，则将字母坐标减去50
                    if ((Math.abs(map1.getCoord().get(0) - map.getCoord().get(0)) < 5)
                        && (Math.abs(map1.getCoord().get(1) - map.getCoord()
                        .get(1)) < 5)) {
                        map.getCoord().set(0, map.getCoord().get(0) - 50);
                        map.getCoord().set(1, map.getCoord().get(1) - 50);
                    }
                }
            }
            letterPositions = JSON.toJSONString(lList);

            // 转换组装 mathObjs
            Map<String, Map> pointObjects = new HashMap<>();
            Map<String, Map> lineSegmentObjects = new HashMap<>();
            Map<String, Map> straightLineObjects = new HashMap<>();
            Map<String, Map> cartesianCoordinateSystemObjects = new HashMap<>();
            BuildObjectsAndRelationsContext b = new BuildObjectsAndRelationsContext();
            b.setRsponseMap(llmResponseMap);
            b.setPointObjects(pointObjects);
            b.setLineSegmentObjects(lineSegmentObjects);
            b.setStraightLineObjects(straightLineObjects);
            b.setCartesianCoordinateSystemObjects(cartesianCoordinateSystemObjects);
            b.setLineInfoList(JSON.parseObject(lines, List.class));
            b.setInaccuratePoints(pList);
            b.setOcrImageUrl(graphicKitsReq.getOcrImageUrl());
            log.info("doGkTask getAprGraphicKits gkId {} BuildObjectsAndRelationsParam {}", gkId,
                JSONObject.toJSONString(b));
            Map<String, List<Map>> mathObjsMap = null;
            try {
                mathObjsMap = buildObjectAndRelationService.buildObjectsAndRelations(b);
            } catch (Exception e) {
                log.error("doGkTask buildObjectsAndRelations fail gkId {}", gkId, e);
                throw new BizException("构建对象和关系失败: " + gkId + " " + e.getMessage());
            }

            String mathObjs = JSONObject.toJSONString(mathObjsMap);
            log.info("doGkTask getAprGraphicKits gkId {} mathObjs {}", gkId, mathObjs);

            // 补充解析几何点
            Map<String, String> idNameMap = new HashMap<>();
            for (Map map : mathObjsMap.get("objects")) {
                if (Objects.equals(map.get("category"), "dot")) {
                    idNameMap.put(map.get("id").toString(), map.get("name").toString());
                }
            }
            for (Map map : mathObjsMap.get("objects")) {
                if (Objects.equals(map.get("category"), "dot") && Objects.equals(map.get("needCalculate"), 1)) {
                    PointDTO oPoint = null;
                    if (!b.getCartesianCoordinateSystemObjects().values().isEmpty()) {
                        for (PointDTO pointDTO : pList) {
                            if (pointDTO.getName().equals(idNameMap.get(
                                ((Map) ((List) new ArrayList<>(
                                    b.getCartesianCoordinateSystemObjects().values())
                                    .get(0).get("determining_points")).get(0)).get("id").toString()))) {
                                oPoint = pointDTO;
                            }
                        }
                    }
                    if (oPoint == null) {
                        break;
                    }

                    if (new ArrayList<>(
                        b.getCartesianCoordinateSystemObjects().values())
                        .get(0).get("scale") == null) {
                        break;
                    }
                    BigDecimal scale = new BigDecimal(new ArrayList<>(
                        b.getCartesianCoordinateSystemObjects().values())
                        .get(0).get("scale").toString());

                    PointDTO p = new PointDTO();
                    PointDTO op = JSON.parseObject(JSON.toJSONString(oPoint), PointDTO.class);
                    p.setName(map.get("name").toString());
                    List<Integer> l = new ArrayList<>();
                    l.add((int) (op.getCoord().get(0) + (Double) (((Double) map.get("qx")) * scale.doubleValue())));
                    l.add((int) (op.getCoord().get(1) + (Double) (((Double) map.get("qy")) * scale.doubleValue())));
                    p.setCoord(l);
                    pList.add(p);
                }
            }

            // 获取测绘点和大模型提取点的交集
            inaccuratePoints = JSON.toJSONString(pList);

            // 根据几何关系信息获取矫正方程组
            String extractEquation = correctCoordinateService.extractEquation(mathObjs);
            if (StringUtils.isBlank(extractEquation)) {
                log.error("doGkTask extractEquation failed gkId {} {}", gkId, mathObjs);
                throw new BizException("获取矫正方程失败" + " " + gkId);
            }

            // 获取矫正坐标
            String actualPoints = correctCoordinateService.correctCoordinate(mathObjs, inaccuratePoints);
            if (StringUtils.isBlank(actualPoints)) {
                log.error("doGkTask correctCoordinate failed gkId {} {} {}", gkId, mathObjs, inaccuratePoints);
                return null;
            }

            // 点，线段，圆，弧，四边形，多边形，对应的点名称几何
            Map<String, Set<String>> stringSetMap = MathObjesPointOverLapUtil.BuildMaps(mathObjsMap);
            // 矫正以后的点几何
            // Map<String, List<BigDecimal>> map1 = new HashMap<>();
            Map<String, List<BigDecimal>> map1 = JSONObject.parseObject(actualPoints, Map.class);

            // 点重合
            MathObjesPointOverLapUtil.BuildMathObjsPointOverLap(gkId, map1, stringSetMap);

            // 所有点共线
            MathObjesPointOverLapUtil.checkPointsCollinear(gkId, map1, stringSetMap);

            // 拿到当前点集合

            // 抛物线对象提取处理
            Map<String, List<BigDecimal>> map2 = JSONObject.parseObject(actualPoints, Map.class);
            // 循环便利map2
            List<PointSparnll> pointDTOS = new ArrayList<>();
            for (Entry<String, List<BigDecimal>> entry : map2.entrySet()) {
                PointSparnll pointDTO = new PointSparnll();
                pointDTO.setName(entry.getKey());
                pointDTO.setCoord(entry.getValue());
                pointDTOS.add(pointDTO);
            }
            // 提取抛物线对象
            Map<String, Map> parabolaObjects = buildObjectAndRelationService.extractParabolaObjects(llmResponseMap,
                pointObjects, pointDTOS);
            if (parabolaObjects != null) {
                mathObjsMap.get("objects").addAll(parabolaObjects.values());
            }
            // 补充提取解析几何直线对象
            buildObjectAndRelationService.extractAnalyticGeometryStraightLineObjects(b, llmResponseMap,
                pointDTOS);
            Map<String, List<BigDecimal>> actualPointsMap = new HashMap<>();
            // 遍历 pointDTOS 并将数据放入 map 中
            for (PointSparnll pointDTO : pointDTOS) {
                actualPointsMap.put(pointDTO.getName(), pointDTO.getCoord());
            }
            // 将 map 序列化为 JSON 字符串
            actualPoints = JSONObject.toJSONString(actualPointsMap);
            List<Map> l1 = mathObjsMap.get("objects");
            List<Map> ll3 = new ArrayList<>();
            for (Map map : l1) {
                if (!Objects.equals(map.get("category"), "dot") && !Objects.equals(map.get("category"),
                    "straightLine")) {
                    ll3.add(map);
                }
            }
            ll3.addAll(b.getPointObjects().values());
            ll3.addAll(b.getStraightLineObjects().values());
            mathObjsMap.put("objects", ll3);

            // 过滤临时对象和关系
            // Map<String, String> idNameMap = new HashMap<>();
            Map<String, List<BigDecimal>> ap = JSON.parseObject(actualPoints, Map.class);
            List<Map> l2 = new ArrayList<>();
            for (Map map : mathObjsMap.get("objects")) {
                // 根据距离判断过滤无效的圆弧对象
                // if (Objects.equals(map.get("category"), "dot")) {
                //     idNameMap.put(map.get("id").toString(), map.get("name").toString());
                // }
                // 处理多点圆弧，特征点顺序，吧距离最大两个点排前面
                if (Objects.equals(map.get("category"), "arc")) {
                    List<Map<String, String>> lm = (List) map.get("determining_points");
                    if (lm.size() > 3) {
                        int inedx1 = 0;
                        int inedx2 = 0;
                        double distanceMax = 0.0;
                        for (int ii = 1; ii < lm.size() - 1; ii++) {
                            for (int j = ii + 1; j < lm.size(); j++) {
                                List<BigDecimal> aa = ap.get(preprocessParamName(idNameMap.get(lm.get(ii).get("id"))));
                                List<BigDecimal> bb = ap.get(preprocessParamName(idNameMap.get(lm.get(j).get("id"))));
                                // 提取坐标
                                BigDecimal aaX = aa.get(0);
                                BigDecimal aaY = aa.get(1);
                                BigDecimal bbX = bb.get(0);
                                BigDecimal bbY = bb.get(1);
                                // 计算 aa 和 bb 之间的距离
                                double distanceAaBb = Math.sqrt(
                                    Math.pow(aaX.doubleValue() - bbX.doubleValue(), 2) + Math.pow(
                                        aaY.doubleValue() - bbY.doubleValue(), 2));
                                if (distanceMax < distanceAaBb) {
                                    distanceMax = distanceAaBb;
                                    inedx1 = ii;
                                    inedx2 = j;
                                }
                            }
                        }
                        // 假设 lm 是一个 List<Map<String, String>> 类型的数组
                        if (inedx1 != 1) {
                            // 交换 index1 和第二个位置的元素
                            Map<String, String> temp = lm.get(inedx1);
                            lm.set(inedx1, lm.get(1));
                            lm.set(1, temp);
                        }
                        if (inedx2 != 2) {
                            // 交换 index2 和第三个位置的元素
                            Map<String, String> temp = lm.get(inedx2);
                            lm.set(inedx2, lm.get(2));
                            lm.set(2, temp);
                        }
                    }
                }
                if (Objects.equals(map.get("type"), "isTemp")) {
                    continue;
                }
                l2.add(map);
            }
            mathObjsMap.put("objects", l2);

            List<Map> l3 = mathObjsMap.get("relations");
            List<Map> l4 = new ArrayList<>();
            for (Map map : l3) {
                if (Objects.equals(map.get("isTemp"), 1)) {
                    continue;
                }
                l4.add(map);
            }
            mathObjsMap.put("relations", l4);
            mathObjs = JSONObject.toJSONString(mathObjsMap);

            // 保存成功数据
            saveAprGraphicKit(graphicKitsReq, mathObjs, actualPoints, extractEquation,
                inaccuratePoints,
                // inaccuratePointsRaw,
                // letterPositionsRaw,
                letterPositions,
                lines,
                gkId, 1);

            GraphicKitsRecordPO graphicKitsRecordPO = aprGraphicKitsDao.selectById(Long.parseLong(gkId));
            BurialUtil.modelLog("METHOD",
                "com.jzx.aiagents.service.components.graphickits.impl.GraphicKitsAnalysisServiceImpl.doGkTask",
                "GK",
                "GK对象/关系解析", "BUILD_GK_INFO",
                JSON.toJSONString(graphicKitsReq),
                JSON.toJSONString(graphicKitsRecordPO),
                startTime,
                null, LocalDateTime.now(), true);
            log.info("doGkTask 成功 gkId {}", gkId);
        } catch (Exception e) {
            // 保存成功数据
            saveAprGraphicKit(graphicKitsReq, null, null, null,
                null,
                null,
                null,
                gkId, 2);
            BurialUtil.modelLog("METHOD",
                "com.jzx.aiagents.service.components.graphickits.impl.GraphicKitsAnalysisServiceImpl.doGkTask",
                "GK",
                "GK对象/关系解析", "BUILD_GK_INFO",
                JSON.toJSONString(graphicKitsReq),
                e.getMessage(),
                startTime,
                null, LocalDateTime.now(), false);
            log.error("doGkTask fail 构建对象和关系失败 gkId {}", gkId, e);
        }
        return gkId;
    }


    public static String preprocessParamName(String param) {
        return param.replaceAll("[^a-zA-Z0-9_]", "_");
    }

    /**
     * 并发获取大模型对象关系结果
     *
     * @param graphicKitsReq
     * @return
     */
    @NotNull
    @AutoLog
    private ConcurrentHashMap<String, String> getAllGkInfoDTOs(String gkId, GraphicKitsReq graphicKitsReq) {
        // LocalDateTime startTime = LocalDateTime.now();
        ApiKeyGkSceneEnum[] values = ApiKeyGkSceneEnum.values();
        List<CompletableFuture<String>> futures = new ArrayList<>();
        for (ApiKeyGkSceneEnum value : values) {
            if (value.getIsEnable()) {
                String question = "";
                // 只需要题干场景
                boolean isMatch = Stream.of(ApiKeyGkSceneEnum.POINT_RELATION, ApiKeyGkSceneEnum.CIRCULAR_ARC,
                        ApiKeyGkSceneEnum.CIRCLE, ApiKeyGkSceneEnum.COMPASS_ARC,
                        ApiKeyGkSceneEnum.POINT_RELATION_SPEL)
                    .anyMatch(ApiKeyGkSceneEnum -> ApiKeyGkSceneEnum.getType().equals(value.getType()));

                if (isMatch) {
                    question = graphicKitsReq.getTerm();
                } else {
                    if (graphicKitsReq.getMethod() == null){
                        graphicKitsReq.setMethod("");
                        // question = graphicKitsReq.getTerm() + graphicKitsReq.getMethod() + graphicKitsReq.getAnswer();
                    }
                    if (graphicKitsReq.getAnswer() == null){
                        graphicKitsReq.setAnswer("");
                        // question = graphicKitsReq.getTerm() + graphicKitsReq.getMethod() + graphicKitsReq.getAnswer();
                    }
                    question = graphicKitsReq.getTerm() + graphicKitsReq.getMethod() + graphicKitsReq.getAnswer();
                }

                Map<String, Object> inputs = new HashMap<>();
                inputs.put("question", question);

                CompletableFuture<String> future = CompletableFuture.supplyAsync(() ->
                    getGkInfoDTOBySceneCode(gkId, graphicKitsReq, value.getType(), inputs), gkTaskExecutor);
                futures.add(future);
            }
        }

        // 等待所有任务完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));

        // 获取所有任务的结果
        CompletableFuture<List<String>> allResults = allFutures.thenApply(v ->
            futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList()));

        ConcurrentHashMap<String, String> llmResponseMap = new ConcurrentHashMap<>();
        try {
            List<String> results = allResults.get();
            for (String result : results) {
                ConcurrentHashMap concurrentHashMap = JSONObject.parseObject(result, ConcurrentHashMap.class);
                llmResponseMap.putAll(concurrentHashMap != null ? concurrentHashMap : new ConcurrentHashMap());
            }
        } catch (InterruptedException | ExecutionException e) {
            e.printStackTrace();
        }
        // BurialUtil.modelLog("MODEL_LOG",
        //     "com.jzx.aiagents.service.components.graphickits.impl.GraphicKitsAnalysisServiceImpl.getAllGkInfoDTOs",
        //     "GK",
        //     "GK对象/关系提取汇总结果", "GET_GK_INFO_ALL",
        //     "GraphicKitsReq:" + JSON.toJSONString(graphicKitsReq),
        //     JSON.toJSONString(llmResponseMap),
        //     startTime,
        //     null, LocalDateTime.now(), true);
        return llmResponseMap;
    }

    @Nullable
    private String getGkInfoDTOBySceneCode(String gkId, GraphicKitsReq graphicKitsReq, String sceneCode,
        Map<String, Object> inputs) {
        LocalDateTime startTime = LocalDateTime.now();
        String answer = null;
        ConcurrentHashMap<String, String> hashMap = new ConcurrentHashMap<>();
        try {
            answer = difyServiceHelper.getDifyResult(graphicKitsReq, sceneCode, inputs, Collections.emptyList());
            answer = answer.replaceAll("```json", "");
            answer = answer.replaceAll("```", "");
        } catch (Exception e) {
            log.error("getGkRelationDTO fail gkId {} {}", gkId, e.getMessage(), e);
            Map m = JSON.parseObject(JSON.toJSONString(graphicKitsReq), Map.class);
            m.put("scene", ApiKeyGkSceneEnum.getNameByType(
                sceneCode));
            m.put("inputs", JSON.toJSONString(inputs));
            m.put("gkId", gkId);
            BurialUtil.modelLog("MODEL_LOG",
                "com.jzx.aiagents.service.components.graphickits.impl.GraphicKitsAnalysisServiceImpl.getGkInfoDTOBySceneCode",
                "GK",
                "GK对象/关系提取", "GET_GK_INFO_BY_SCENE_CODE",
                JSON.toJSONString(m),
                answer + " 报错信息:" + e.getMessage(),
                startTime,
                null, LocalDateTime.now(), false);
        }
        if (StringUtils.isNotBlank(answer)) {
            hashMap.put(sceneCode, answer);
        }
        Map m = JSON.parseObject(JSON.toJSONString(graphicKitsReq), Map.class);
        m.put("scene", ApiKeyGkSceneEnum.getNameByType(
            sceneCode));
        m.put("inputs", JSON.toJSONString(inputs));
        m.put("gkId", gkId);
        BurialUtil.modelLog("MODEL_LOG",
            "com.jzx.aiagents.service.components.graphickits.impl.GraphicKitsAnalysisServiceImpl.getGkInfoDTOBySceneCode",
            "GK",
            "GK对象/关系提取", "GET_GK_INFO_BY_SCENE_CODE",
            JSON.toJSONString(m),
            answer,
            startTime,
            null, LocalDateTime.now(), true);
        // 将hashMap转成Json字符串
        return JSON.toJSONString(hashMap);
    }

    private void saveAprGraphicKit(GraphicKitsReq graphicKitsReq, String mathObjs, String actualPoints,
        String extractEquation, String inaccuratePoints, String gkPoints, String lines, String id, int status) {
        LocalDateTime ldt = LocalDateTime.now();
        GraphicKitsRecordPO graphicKitsRecordPO = new GraphicKitsRecordPO();
        graphicKitsRecordPO.setId(Long.valueOf(id));
        graphicKitsRecordPO.setMathObjs(mathObjs);
        graphicKitsRecordPO.setLetterPoints(gkPoints);
        graphicKitsRecordPO.setLinesData(lines);
        graphicKitsRecordPO.setCreateTime(ldt);
        graphicKitsRecordPO.setModifyTime(ldt);
        graphicKitsRecordPO.setInaccuratePoints(inaccuratePoints);
        graphicKitsRecordPO.setStatus(status);
        graphicKitsRecordPO.setActualPoints(actualPoints);
        graphicKitsRecordPO.setExtractEquation(extractEquation);

        if (graphicKitsReq != null) {
            // aprGraphicKitsPO.setRoomId(aiPreResearch.getRoomId());
            // aprGraphicKitsPO.setRoundId(aiPreResearch.getRoundId());
            graphicKitsRecordPO.setPid(graphicKitsReq.getPid());
            graphicKitsRecordPO.setQid(graphicKitsReq.getQid());
            graphicKitsRecordPO.setTerm(graphicKitsReq.getTerm());
            graphicKitsRecordPO.setMethod(graphicKitsReq.getMethod());
            graphicKitsRecordPO.setAnswer(graphicKitsReq.getAnswer());
            graphicKitsRecordPO.setImageUrl(graphicKitsReq.getImageUrl());
            graphicKitsRecordPO.setOcrImageUrl(graphicKitsReq.getOcrImageUrl());
            graphicKitsRecordPO.setLectureQuestionId(graphicKitsReq.getLectureQuestionId());
            graphicKitsRecordPO.setModifyUser(graphicKitsReq.getUserId());
            graphicKitsRecordPO.setModifyUser(graphicKitsReq.getUserId());
        }
        aprGraphicKitsDao.saveGraphicKitInfo(graphicKitsRecordPO);
    }
}
