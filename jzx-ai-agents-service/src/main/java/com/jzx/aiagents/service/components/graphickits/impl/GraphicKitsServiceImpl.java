package com.jzx.aiagents.service.components.graphickits.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.jzx.aiagents.api.components.dify.request.DifyFileRequest;
import com.jzx.aiagents.api.components.graphickits.dto.GkDetailInfoDTO;
import com.jzx.aiagents.api.components.graphickits.dto.GkRelationDTO;
import com.jzx.aiagents.api.components.graphickits.dto.GkRelationsInfo;
import com.jzx.aiagents.api.components.graphickits.req.GetGKAccessControllerReq;
import com.jzx.aiagents.api.components.graphickits.req.GkDetailInfoQuery;
import com.jzx.aiagents.api.components.graphickits.req.GkRelationReq;
import com.jzx.aiagents.api.model.enums.AILectureQuestionRouteEnum;
import com.jzx.aiagents.api.model.enums.graphickits.ApiKeyGkOtherEnum;
import com.jzx.aiagents.api.model.enums.graphickits.ApiKeyGkSceneEnum;
import com.jzx.aiagents.dao.api.GraphicKitsDao;
import com.jzx.aiagents.dao.api.GraphicKitsRelationDao;
import com.jzx.aiagents.dao.model.po.GraphicKitsRecordPO;
import com.jzx.aiagents.dao.model.po.GraphicKitsRelationPO;
import com.jzx.aiagents.service.components.dify.api.DifyService;
import com.jzx.aiagents.service.components.dify.helper.DifyServiceHelper;
import com.jzx.aiagents.service.components.graphickits.api.GraphicKitsService;
import com.jzx.aiagents.service.components.tools.JsonFixedService;
import com.jzx.common.burial.util.BurialUtil;
import com.jzx.common.lang.exception.BizException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2025/5/22 14:58
 */
@Service
@RequiredArgsConstructor
@Slf4j
@RefreshScope
public class GraphicKitsServiceImpl implements GraphicKitsService {


    @Autowired
    private GraphicKitsDao aprGraphicKitsDao;

    @Autowired
    protected DifyServiceHelper difyServiceHelper;

    @Autowired
    protected DifyService difyService;
    @Autowired
    private JsonFixedService jsonFixedService;
    @Autowired
    private GraphicKitsRelationDao graphicKitsRelationDao;

    @Autowired
    @Qualifier("aiAgentsExecutor")
    private ThreadPoolTaskExecutor interactiveLessonExecutor;

    public static String preprocessParamName(String param) {
        return param.replaceAll("[^a-zA-Z0-9_]", "_");
    }

    @Override
    public GkDetailInfoDTO getGkDetailInfo(GkDetailInfoQuery gkDetailInfoQuery) {
        if (StringUtils.isBlank(gkDetailInfoQuery.getGkId())) {
            throw new BizException("参数错误 gkId 不可为空");
        }
        String gkId = gkDetailInfoQuery.getGkId();
        GraphicKitsRecordPO graphicKitsRecordPO = aprGraphicKitsDao.selectById(Long.parseLong(gkId));
        if (Objects.isNull(graphicKitsRecordPO)) {
            // throw new BizException(gkDetailInfoQuery.getGkId() + " 未获取到GK关系数据");
            return null;
        }
        GkDetailInfoDTO gkDetailInfoDTO = new GkDetailInfoDTO();
        gkDetailInfoDTO.setGkId(gkId);
        gkDetailInfoDTO.setStatus(graphicKitsRecordPO.getStatus());
        if (StringUtils.isNotEmpty(graphicKitsRecordPO.getMathObjs())) {
            JSONObject m1 = JSONObject.parseObject(graphicKitsRecordPO.getMathObjs());
            JSONObject m2 = JSONObject.parseObject(graphicKitsRecordPO.getActualPoints());
            List l3 = JSONObject.parseObject(graphicKitsRecordPO.getInaccuratePoints(), List.class);
            List l4 = JSONObject.parseObject(graphicKitsRecordPO.getLetterPoints(), List.class);
            List<Map<String, Object>> objects = (List<Map<String, Object>>) m1.get("objects");

            for (Map<String, Object> obj : objects) {
                if ("dot".equals(obj.get("category")) && m2.containsKey(
                    preprocessParamName((String) obj.get("name")))) {
                    List point = (List) m2.get(preprocessParamName((String) obj.get("name")));
                    obj.put("x", point.get(0));
                    obj.put("y", point.get(1));
                }
                for (Object o : l3) {
                    Map<String, Object> map = (Map<String, Object>) o;
                    if (map.get("name").equals(preprocessParamName((String) obj.get("name")))) {
                        obj.put("rx", ((List) map.get("coord")).get(0));
                        obj.put("ry", ((List) map.get("coord")).get(1));
                        break;
                    }
                }
                for (Object o : l4) {
                    Map<String, Object> map = (Map<String, Object>) o;
                    if (map.get("name").equals(preprocessParamName((String) obj.get("name")))) {
                        obj.put("lx", ((List) map.get("coord")).get(0));
                        obj.put("ly", ((List) map.get("coord")).get(1));
                        break;
                    }
                }
            }
            gkDetailInfoDTO.setMathObjs(JSONObject.toJSONString(m1));
        }
        return gkDetailInfoDTO;
    }

    @Override
    public GkRelationDTO getGkRelation(GkRelationReq gkRelationReq) {
        LocalDateTime startTime = LocalDateTime.now();
        log.info("com.jzx.aiagents.service.components.graphickits.impl.GraphicKitsServiceImpl.getGkRelation:gkRelationReq{}",  JSON.toJSONString(gkRelationReq));
        if (StringUtils.isBlank(gkRelationReq.getGkId())) {
            throw new BizException("参数错误 gkId 不可为空");
        }
        GkRelationDTO gkRelationResponse = new GkRelationDTO();
        // 获取gk关系数据
        GraphicKitsRecordPO graphicKitsRecordPO = aprGraphicKitsDao.selectById(Long.parseLong(gkRelationReq.getGkId()));
        log.info("com.jzx.aiagents.service.components.graphickits.impl.GraphicKitsServiceImpl.getGkRelation:获取gk关系数据graphicKitsRecordPO{}", JSON.toJSONString(graphicKitsRecordPO));
        if (graphicKitsRecordPO != null && StringUtils.isNotEmpty(graphicKitsRecordPO.getMathObjs())) {
            // 获取入参
            Map<String, Object> inputs = getInputs(gkRelationReq, graphicKitsRecordPO);
            ApiKeyGkOtherEnum[] values = ApiKeyGkOtherEnum.values();
            List<ApiKeyGkOtherEnum> apiKeyGkOtherEnums = Arrays.stream(values)
                .filter(e -> ApiKeyGkOtherEnum.DYNAMIC_SIGNAGE_OBJECT.getType().equals(e.getType())
                    || ApiKeyGkOtherEnum.DYNAMIC_SIGNAGE_RELATION.getType()
                    .equals(e.getType())
                ).collect(Collectors.toList());
            // 并发调用模型结果
            List<CompletableFuture<GkRelationDTO>> futures = apiKeyGkOtherEnums.stream().map(
                e -> CompletableFuture.supplyAsync(() -> getGkRelationDTO(gkRelationReq, e, inputs),
                    interactiveLessonExecutor)).toList();
            // 等待所有任务完成
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));

            // 获取所有任务的结果
            CompletableFuture<List<GkRelationDTO>> allResults = allFutures.thenApply(v ->
                futures.stream()
                    .map(CompletableFuture::join).collect(Collectors.toList()));

            try {
                List<GkRelationDTO> results = allResults.get();
                for (GkRelationDTO result : results) {
                    if (result != null) {
                        if (!CollectionUtils.isEmpty(result.getRelationShips())) {
                            gkRelationResponse.setRelationShips(result.getRelationShips());
                        }
                        if (!CollectionUtils.isEmpty(result.getValues())) {
                            gkRelationResponse.setValues(result.getValues());
                        }
                        log.info("com.jzx.aiagents.service.components.graphickits.impl.GraphicKitsServiceImpl.getGkRelation 动态标识结果 result{}", JSON.toJSONString(result));
                    }
                }
                log.info("com.jzx.aiagents.service.components.graphickits.impl.GraphicKitsServiceImpl.getGkRelation 动态标识结果 results{}", JSON.toJSONString(results));
            } catch (Exception e) {
                log.error("Error occurred while waiting for results", e);
                BurialUtil.modelLog("MODEL_LOG", "com.jzx.aiagents.service.components.graphickits.impl.GraphicKitsServiceImpl.getGkRelation", "GK",
                    "动态标识", "LLM",  JSON.toJSONString(gkRelationReq),JSON.toJSONString(e.getMessage()),
                    startTime,
                    null, LocalDateTime.now(), false);
            }
            BurialUtil.modelLog("MODEL_LOG", "com.jzx.aiagents.service.components.graphickits.impl.GraphicKitsServiceImpl.getGkRelation", "GK",
                "动态标识", "LLM",  JSON.toJSONString(gkRelationReq),JSON.toJSONString(gkRelationResponse),
                startTime,
                null, LocalDateTime.now(), true);
            // 保存数据库到数据库
            GraphicKitsRelationPO graphicKitsRelationPO = new GraphicKitsRelationPO();
            BeanUtils.copyProperties(gkRelationReq, graphicKitsRelationPO);
            graphicKitsRelationPO.setGkRelation(JSONObject.toJSONString(gkRelationResponse));
            graphicKitsRelationDao.saveGraphicKitsRelation(graphicKitsRelationPO);
            log.info("com.jzx.aiagents.service.components.graphickits.impl.GraphicKitsServiceImpl.getGkRelation:graphicKitsRelationPO{}", JSON.toJSONString(graphicKitsRelationPO));
            return gkRelationResponse;
        } else {
            return null;
        }
    }

    /**
     * GK准入器 返回 0不通过 1通过
     * @param getGkAccessControllerReq
     * @return
     */
    @Override
    public String getGKAccessController(GetGKAccessControllerReq getGkAccessControllerReq) {
        // 帮我创建一个并发map
        Map<String, String> concurrentMap = new ConcurrentHashMap<>();
        LocalDateTime startTime = LocalDateTime.now();
        Map<String, Object> inputs = new HashMap<>();
        getGkAccessControllerReq.setBizId(getGkAccessControllerReq.getGkId());
        inputs.put("questionstem", getGkAccessControllerReq.getTerm());
        inputs.put("questionMethodAnswer",
            getGkAccessControllerReq.getMethod() + " " + getGkAccessControllerReq.getAnswer());
        // 文件
        String answer = "";
        List<DifyFileRequest> files = getDifyFileRequests(getGkAccessControllerReq);
        log.info(
            "com.jzx.aiagents.service.components.graphickits.impl.GraphicKitsServiceImpl.getGKAccessController: Gk准入器 req {} files:{}",
            JSON.toJSONString(getGkAccessControllerReq), files);
        try {
            ApiKeyGkOtherEnum[] values = ApiKeyGkOtherEnum.values();
            if (AILectureQuestionRouteEnum.isQuark(getGkAccessControllerReq.getRoute())){
                // values帮我把枚举 GK_QUARK_ACCESS_CONTROLLER_QUESTIONSTEM，GK_QUARK_ACCESS_CONTROLLER_QUESTIONMETHODANSWER， GK_QUARK_ACCESS_CONTROLLER_FILE 取出这三个出来
                List<ApiKeyGkOtherEnum> collect = Arrays.stream(values)
                    .filter(e -> ApiKeyGkOtherEnum.GK_QUARK_ACCESS_CONTROLLER_QUESTIONSTEM.getType().equals(e.getType())
                        || ApiKeyGkOtherEnum.GK_QUARK_ACCESS_CONTROLLER_QUESTIONMETHODANSWER.getType()
                        .equals(e.getType()) ||  ApiKeyGkOtherEnum.GK_QUARK_ACCESS_CONTROLLER_FILE.getType()
                        .equals(e.getType())
                    ).collect(Collectors.toList());
                List<CompletableFuture<Map<String, String>>> futures = collect.stream().map(
                    e -> CompletableFuture.supplyAsync(
                        () -> quarkController(getGkAccessControllerReq, e, concurrentMap),
                        interactiveLessonExecutor)).collect(Collectors.toList());
                // 等待所有任务完成提取值
                CompletableFuture<List<Map<String, String>>> allResults = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0])).thenApply(v ->
                    futures.stream().map(CompletableFuture::join).collect(Collectors.toList()));
                List<Map<String, String>> maps = allResults.get();
                log.info("com.jzx.aiagents.service.components.graphickits.impl.GraphicKitsServiceImpl.getGKAccessController: 夸克路径 getGkAccessControllerReq:{} Gk准入器maps:{}", JSON.toJSONString(getGkAccessControllerReq),JSON.toJSONString(maps));
                BurialUtil.modelLog("MODEL_LOG", "com.jzx.aiagents.service.components.graphickits.impl.GraphicKitsServiceImpl.getGKAccessController", "GK",
                    "GK准入器-quark", "LLM", JSON.toJSONString(getGkAccessControllerReq), allResults.toString(),
                    startTime,
                    null, LocalDateTime.now(), true);
                if (!concurrentMap.isEmpty()) {
                    String s = concurrentMap.get(ApiKeyGkOtherEnum.GK_QUARK_ACCESS_CONTROLLER_FILE.getType());
                    List<String> points = getGkAccessControllerReq.getPoints();
                    // 1:需要将s转成集合 List<String> s1  ,2: s1中的元素和points 是否所有元素是否都一样
                    List<String> s1 = JSON.parseArray(s, String.class);
                    // s1中的元素和points中的元素是否相等
                    if (!Objects.equals(points.size(), s1.size()) || !new HashSet<>(points).containsAll(s1)
                        || !s1.containsAll(new HashSet<>(points))) {
                        answer = "0";
                        return answer;
                    }
                    String s2 = concurrentMap.get(
                        ApiKeyGkOtherEnum.GK_QUARK_ACCESS_CONTROLLER_QUESTIONMETHODANSWER.getType());
                    if (s2.equals("0")) {
                        answer = "0";
                        return answer;
                    }
                    String s3 = concurrentMap.get(ApiKeyGkOtherEnum.GK_QUARK_ACCESS_CONTROLLER_QUESTIONSTEM.getType());
                    if (s3.equals("0")) {
                        answer = "0";
                        return answer;
                    }
                }
                answer = "1";
                return answer;
            }else{
                // ocr准入器
                List<ApiKeyGkOtherEnum> collect = Arrays.stream(values)
                    .filter(e -> ApiKeyGkOtherEnum.GK_OCR_ACCESS_CONTROLLER_QUESTIONSTEM.getType().equals(e.getType())
                        || ApiKeyGkOtherEnum.GK_OCR_ACCESS_CONTROLLER_QUESTIONMETHODANSWER.getType()
                        .equals(e.getType()) || ApiKeyGkOtherEnum.GK_OCR_ACCESS_CONTROLLER_FILE.getType()
                        .equals(e.getType())
                    ).collect(Collectors.toList());
                List<CompletableFuture<Map<String, String>>> futures = collect.stream().map(
                    e -> CompletableFuture.supplyAsync(
                        () -> getOcrController(getGkAccessControllerReq, e, concurrentMap),
                        interactiveLessonExecutor)).collect(Collectors.toList());
                // 等待所有任务完成提取值
                // 等待所有任务完成提取值
                CompletableFuture<List<Map<String, String>>> allResults = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0])).thenApply(v ->
                    futures.stream().map(CompletableFuture::join).collect(Collectors.toList()));
                // strings集合中所有的值为每一个对象为1 answer才是1, 有任何一个是0 answer都为0
                List<Map<String, String>> maps = allResults.get();
                log.info("com.jzx.aiagents.service.components.graphickits.impl.GraphicKitsServiceImpl.getGKAccessController: 夸克路径 getGkAccessControllerReq:{} Gk准入器maps:{}",JSON.toJSONString(getGkAccessControllerReq), JSON.toJSONString(maps));
                BurialUtil.modelLog("MODEL_LOG", "com.jzx.aiagents.service.components.graphickits.impl.GraphicKitsServiceImpl.getGKAccessController", "GK",
                    "GK准入器-ocr", "LLM",  JSON.toJSONString(getGkAccessControllerReq), maps.toString(),
                    startTime,
                    null, LocalDateTime.now(), true);
                if (!concurrentMap.isEmpty()){
                    String s = concurrentMap.get(ApiKeyGkOtherEnum.GK_OCR_ACCESS_CONTROLLER_FILE.getType());
                    List<String> points = getGkAccessControllerReq.getPoints();
                    // 1:需要将s转成集合 List<String> s1  ,2: s1中的元素和points 是否所有元素是否都一样
                    List<String> s1 = JSON.parseArray(s, String.class);
                    // s1中的元素和points中的元素是否相等
                    if (!Objects.equals(points.size(),s1.size()) || !new HashSet<>(points).containsAll(s1) || !s1.containsAll(new HashSet<>(points))) {
                        answer = "0";
                        return answer;
                    }
                    String s2 = concurrentMap.get(ApiKeyGkOtherEnum.GK_OCR_ACCESS_CONTROLLER_QUESTIONMETHODANSWER.getType());
                    if (s2.equals("0")){
                        answer = "0";
                        return answer;
                    }
                    String s3 = concurrentMap.get(ApiKeyGkOtherEnum.GK_OCR_ACCESS_CONTROLLER_QUESTIONSTEM.getType());
                    if (s3.equals("0")){
                        answer = "0";
                        return answer;
                    }
                }
                answer = "1";
                return answer;
            }
        } catch (Exception e) {
            log.info("com.jzx.aiagents.service.components.graphickits.impl.GraphicKitsServiceImpl.getGKAccessController: error:{}", e.getMessage());
            BurialUtil.modelLog("MODEL_LOG", "com.jzx.aiagents.service.components.graphickits.impl.GraphicKitsServiceImpl.getGKAccessController", "GK",
                "GK准入器-" + getGkAccessControllerReq.getRoute(), "LLM", JSON.toJSONString(getGkAccessControllerReq),
                e.getMessage(),
                startTime,
                null, LocalDateTime.now(), false);
        }
        return answer;
    }

    // ocr 准入器
    private Map<String,String> getOcrController(GetGKAccessControllerReq getGkAccessControllerReq,ApiKeyGkOtherEnum e,Map<String, String> concurrentMap) {
        String answer;
        Map<String, Object> inputs = new HashMap<>();
        List<DifyFileRequest> files = null;
        getGkAccessControllerReq.setBizId(getGkAccessControllerReq.getGkId());
        if (ApiKeyGkOtherEnum.GK_OCR_ACCESS_CONTROLLER_QUESTIONSTEM.getType().equals(e.getType())){
            inputs.put("questionstem", getGkAccessControllerReq.getTerm());
        }
        if (ApiKeyGkOtherEnum.GK_OCR_ACCESS_CONTROLLER_QUESTIONMETHODANSWER.getType().equals(e.getType())){
            inputs.put("questionMethodAnswer", getGkAccessControllerReq.getMethod()+getGkAccessControllerReq.getAnswer());
        }
        if (ApiKeyGkOtherEnum.GK_OCR_ACCESS_CONTROLLER_FILE.getType().equals(e.getType())){
            files = getDifyFileRequests(getGkAccessControllerReq);
        }
        answer = difyServiceHelper.getDifyResult(getGkAccessControllerReq,
            e.getType(), inputs, files);
        concurrentMap.put(e.getType(), answer);
        return concurrentMap;

    }

    // quark准入器
    private Map<String,String> quarkController(GetGKAccessControllerReq getGkAccessControllerReq,ApiKeyGkOtherEnum e,Map<String, String> concurrentMap) {
        String answer;
        Map<String, Object> inputs = new HashMap<>();
        List<DifyFileRequest> files = null;
        getGkAccessControllerReq.setBizId(getGkAccessControllerReq.getGkId());
        if (ApiKeyGkOtherEnum.GK_QUARK_ACCESS_CONTROLLER_QUESTIONSTEM.getType().equals(e.getType())){
            inputs.put("questionstem", getGkAccessControllerReq.getTerm());
        }
        if (ApiKeyGkOtherEnum.GK_QUARK_ACCESS_CONTROLLER_QUESTIONMETHODANSWER.getType().equals(e.getType())){
            inputs.put("questionMethodAnswer", getGkAccessControllerReq.getMethod()+getGkAccessControllerReq.getAnswer());
        }
        if (ApiKeyGkOtherEnum.GK_QUARK_ACCESS_CONTROLLER_FILE.getType().equals(e.getType())){
            files = getDifyFileRequests(getGkAccessControllerReq);
        }
        answer = difyServiceHelper.getDifyResult(getGkAccessControllerReq,
           e.getType(), inputs,  files);
        concurrentMap.put(e.getType(), answer);
        return concurrentMap;
    }

    /**
     * 组装文件
     *
     * @param getGkAccessControllerReq
     * @return
     */
    @NotNull
    private static List<DifyFileRequest> getDifyFileRequests(GetGKAccessControllerReq getGkAccessControllerReq) {
        DifyFileRequest difyFileRequest = new DifyFileRequest();
        difyFileRequest.setUrl(getGkAccessControllerReq.getOcrImageUrl());
        difyFileRequest.setTransferMethod("remote_url");
        difyFileRequest.setType("image");
        // difyFileRequest转成集合
        List<DifyFileRequest> files = new ArrayList<>();
        files.add(difyFileRequest);
        return files;
    }

    /**
     * 调用模型获取动态标识过程数据
     * @param gkRelationReq
     * @param e
     * @param inputs
     * @return
     */
    @Nullable
    private GkRelationDTO getGkRelationDTO(GkRelationReq gkRelationReq, ApiKeyGkOtherEnum e,
        Map<String, Object> inputs) {

        LocalDateTime startTime = LocalDateTime.now();
        String difyResult = null;
        try {
            gkRelationReq.setBizCode("GK");
            gkRelationReq.setBizId(gkRelationReq.getGkId());
            difyResult = difyServiceHelper.getDifyResult(gkRelationReq, e.getType(), inputs,
                Collections.emptyList());
        } catch (Exception ex) {
            log.info(
                "com.jzx.aiagents.service.components.graphickits.impl.GraphicKitsServiceImpl.getGkRelationDTO: error:{}",
                ex.getMessage());
            Map m = JSON.parseObject(JSON.toJSONString(gkRelationReq), Map.class);
            m.put("scene", ApiKeyGkSceneEnum.getNameByType(
                e.getName()));
            m.put("inputs", JSON.toJSONString(inputs));
            BurialUtil.modelLog("MODEL_LOG",
                "com.jzx.aiagents.service.components.graphickits.impl.GraphicKitsServiceImpl.getGkRelationDTO",
                "GK",
                e.getName(), "LLM",
                JSON.toJSONString(m),
                difyResult + " 报错信息:" + ex.getMessage(),
                startTime,
                null, LocalDateTime.now(), false);
        }
        Map m = JSON.parseObject(JSON.toJSONString(gkRelationReq), Map.class);
        m.put("scene", ApiKeyGkSceneEnum.getNameByType(
            e.getName()));
        m.put("inputs", JSON.toJSONString(inputs));
        BurialUtil.modelLog("MODEL_LOG", "com.jzx.aiagents.service.components.graphickits.impl.GraphicKitsServiceImpl.getGkRelationDTO", "GK",
            e.getName(), "LLM", JSON.toJSONString(m), difyResult,
            startTime,
            null, LocalDateTime.now(), true);
        difyResult = difyResult.replaceAll("```json", "");
        difyResult = difyResult.replaceAll("```", "");
        if (StringUtils.isNotEmpty(difyResult)) {
            JSONObject j = jsonFixedService.parseObject(difyResult, GkRelationDTO.class);
            GkRelationDTO g = JSON.parseObject(JSONObject.toJSONString(j), GkRelationDTO.class);
            g.setValues(new ArrayList<>());
            if (g.getValue() != null) {
                for (List<String> strings : g.getValue()) {
                    Map mm = new HashMap();
                    mm.put("id", strings.get(0));
                    mm.put("type", strings.get(1));
                    g.getValues().add(mm);
                }
                g.setValue(null);
            }
            return g;
        }
        return null;
    }

    @NotNull
    private static Map<String, Object> getInputs(GkRelationReq gkRelationReq,
        GraphicKitsRecordPO graphicKitsRecordPO) {
        Map<String, Object> inputs = new HashMap<>();
        inputs.put("response", gkRelationReq.getResponseToStudent());
        Map m = JSONObject.parseObject(graphicKitsRecordPO.getMathObjs());
        log.info("m: {}", m);
        List<GkRelationsInfo> l = JSON.parseArray(JSONObject.toJSONString(m.get("relations")), GkRelationsInfo.class);
        Set<String> s = new HashSet<>();
        s.add("parallel_lines");
        s.add("mutual_perpendicular_lines");
        s.add("congruent_segments");
        s.add("equal_angles");
        l = l.stream().filter(r -> s.contains(r.getType())).collect(Collectors.toList());
        inputs.put("relations", JSONObject.toJSONString(l));
        List<Map> l1 = JSON.parseArray(JSONObject.toJSONString(m.get("objects")), Map.class);
        for (Map map : l1) {
            map.remove("determining_points");
            map.remove("shortName");
        }
        inputs.put("values", JSONObject.toJSONString(l1));
        return inputs;
    }
}
