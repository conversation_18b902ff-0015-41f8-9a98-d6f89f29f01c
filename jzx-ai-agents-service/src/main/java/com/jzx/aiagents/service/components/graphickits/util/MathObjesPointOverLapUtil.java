package com.jzx.aiagents.service.components.graphickits.util;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.jzx.common.lang.exception.BizException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @Date 2025/3/26 11:18
 */
@Slf4j
public class MathObjesPointOverLapUtil {


    public static Map<String, Set<String>> BuildMaps(Map<String, List<Map>> mathObjsMap) {
        // 总共的返回对象
        Map<String, Set<String>> hashMap = new HashMap();

        // 所有点的map
        Map<String, String> pintMap = new HashMap();
        // 线段中的点
        Set<String> setSegment = new HashSet();
        // 角中点
        Set<String> setAngle = new HashSet();
        // 三角形
        Set<String> setTriangle = new HashSet();
        // 四边形
        Set<String> setQuadrilatera = new HashSet();
        // 多边形
        Set<String> setPolygon = new HashSet();
        // 圆
        Set<String> setCircle = new HashSet();

        // // 圆弧
        Set<String> setCircleArc = new HashSet();

        List<Map> maps = mathObjsMap.get("objects");
        // 点
        for (Map map : maps) {
            if (map.get("category").equals("dot")) {
                String point_name = (String) map.get("name");
                String point_id = (String) map.get("id");
                pintMap.put(point_id, point_name);

            }
        }
        // 线段， 角，四边形，三角形，圆弧，圆
        for (Map map : maps) {
            // 线段
            if (map.get("category").equals("segment")) {
                List<Map> points1 = (List) map.get("determining_points");
                List<String> ids = points1.stream().map(o -> (String) o.get("id")).collect(Collectors.toList());
                for (String id : ids) {
                    String s = pintMap.get(id);
                    setSegment.add(s);
                }
            }
            // 角
            if (map.get("category").equals("angle")) {
                List<Map> points1 = (List) map.get("determining_points");
                List<String> ids = points1.stream().map(o -> (String) o.get("id")).collect(Collectors.toList());
                for (String id : ids) {
                    String s = pintMap.get(id);
                    setAngle.add(s);
                }
            }

            // 三角形
            if (map.get("category").equals("triangle")) {
                List<Map> points1 = (List) map.get("determining_points");
                List<String> ids = points1.stream().map(o -> (String) o.get("id")).collect(Collectors.toList());
                for (String id : ids) {
                    String s = pintMap.get(id);
                    setTriangle.add(s);
                }
            }

            // 四边形
            if (map.get("category").equals("quadrilateral")) {
                List<Map> points1 = (List) map.get("determining_points");
                List<String> ids = points1.stream().map(o -> (String) o.get("id")).collect(Collectors.toList());
                for (String id : ids) {
                    String s = pintMap.get(id);
                    setQuadrilatera.add(s);
                }
            }

            // 多边形
            if (map.get("category").equals("polygon")) {
                List<Map> points1 = (List) map.get("determining_points");
                List<String> ids = points1.stream().map(o -> (String) o.get("id")).collect(Collectors.toList());
                for (String id : ids) {
                    String s = pintMap.get(id);
                    setPolygon.add(s);
                }

            }
            // 圆弧
            if (map.get("category").equals("arc")) {
                List<Map> points1 = (List) map.get("determining_points");
                List<String> ids = points1.stream().map(o -> (String) o.get("id")).collect(Collectors.toList());
                for (String id : ids) {
                    String s = pintMap.get(id);
                    setCircleArc.add(s);
                }

            }

            // 圆
            if (map.get("category").equals("circle")) {
                List<Map> points1 = (List) map.get("determining_points");
                List<String> ids = points1.stream().map(o -> (String) o.get("id")).collect(Collectors.toList());
                for (String id : ids) {
                    String s = pintMap.get(id);
                    setCircle.add(s);
                }
            }
        }
        hashMap.put("segment", setSegment);
        hashMap.put("angle", setAngle);
        hashMap.put("triangle", setTriangle);
        hashMap.put("quadrilatera", setQuadrilatera);
        hashMap.put("polygon", setPolygon);
        hashMap.put("circle", setCircle);
        hashMap.put("circleArc", setCircleArc);
        return hashMap;
    }

    public static String preprocessParamName(String param) {
        return param.replaceAll("[^a-zA-Z0-9_]", "_");
    }

    // 把所有类型的点全部放到这个几何中去，再去判断哪个类型点位相同
    public static void BuildMathObjsPointOverLap(String gkId, Map<String, List<BigDecimal>> map,
        Map<String, Set<String>> stringSetMap) {
        // 线段
        Set<String> segments = stringSetMap.get("segment");
        // 角
        Set<String> angles = stringSetMap.get("angle");
        // 三角形
        Set<String> triangles = stringSetMap.get("triangle");
        // 四边形
        Set<String> quadrilateras = stringSetMap.get("quadrilatera");
        // 多边形
        Set<String> polygons = stringSetMap.get("polygon");
        // 圆
        Set<String> circles = stringSetMap.get("circle");

        Set<String> arcs = stringSetMap.get("arc");
        // 线段
        if (!CollectionUtils.isEmpty(segments)) {
            HashMap<String, List<BigDecimal>> segmentHashMap = new HashMap<>();
            for (String segment : segments) {
                String s = preprocessParamName(segment);
                List<BigDecimal> bigDecimals = map.get(s);
                segmentHashMap.put(s, bigDecimals);
            }
            Map<String, List<BigDecimal>> stringListMap = filterDuplicatePoints(gkId, segmentHashMap);
            if (CollectionUtils.isEmpty(stringListMap)) {
                log.info("线段点位重合 {} {}", gkId, JSONObject.toJSONString(stringListMap));
                throw new BizException("线段点位重合" + " " + gkId);
            }
        }

        // 角
        if (!CollectionUtils.isEmpty(angles)) {
            HashMap<String, List<BigDecimal>> anglesHashMap = new HashMap<>();
            for (String angle : angles) {
                String s = preprocessParamName(angle);
                List<BigDecimal> bigDecimals = map.get(s);
                anglesHashMap.put(angle, bigDecimals);
            }
            Map<String, List<BigDecimal>> stringListMap = filterDuplicatePoints(gkId, anglesHashMap);
            if (CollectionUtils.isEmpty(stringListMap)) {
                log.info("角点重合 {} {}", gkId, JSONObject.toJSONString(stringListMap));
                throw new BizException("角点重合" + " " + gkId);
            }
        }

        // 三角形
        if (!CollectionUtils.isEmpty(triangles)) {
            HashMap<String, List<BigDecimal>> trianglesHashMap = new HashMap<>();
            for (String triangle : triangles) {
                String s = preprocessParamName(triangle);
                List<BigDecimal> bigDecimals = map.get(s);
                trianglesHashMap.put(triangle, bigDecimals);
            }
            Map<String, List<BigDecimal>> stringListMap = filterDuplicatePoints(gkId, trianglesHashMap);
            if (CollectionUtils.isEmpty(stringListMap)) {
                log.info("三角形点重合 {} {}", gkId, JSONObject.toJSONString(stringListMap));
                throw new BizException("三角形点重合" + " " + gkId);
            }
        }

        // 圆弧点位重合
        if (!CollectionUtils.isEmpty(arcs)) {
            HashMap<String, List<BigDecimal>> arcHashMap = new HashMap<>();
            for (String arc : arcs) {
                String s = preprocessParamName(arc);
                List<BigDecimal> bigDecimals = map.get(s);
                arcHashMap.put(arc, bigDecimals);
            }
            Map<String, List<BigDecimal>> stringListMap = filterDuplicatePoints(gkId, arcHashMap);
            if (CollectionUtils.isEmpty(stringListMap)) {
                log.info("圆弧点重合 {} {}", gkId, JSONObject.toJSONString(stringListMap));
                throw new BizException("圆弧点重合" + " " + gkId);
            }
        }
        // 四边形点重合
        if (!CollectionUtils.isEmpty(quadrilateras)) {
            HashMap<String, List<BigDecimal>> quadrilaterasHashMap = new HashMap<>();
            for (String quadrilatera : quadrilateras) {
                String s = preprocessParamName(quadrilatera);
                List<BigDecimal> bigDecimals = map.get(s);
                quadrilaterasHashMap.put(quadrilatera, bigDecimals);
            }
            Map<String, List<BigDecimal>> stringListMap = filterDuplicatePoints(gkId, quadrilaterasHashMap);
            if (CollectionUtils.isEmpty(stringListMap)) {
                log.info("四边形重合 {} {}", gkId, JSONObject.toJSONString(stringListMap));
                throw new BizException("四边形重合" + " " + gkId);
            }
        }

        // 多边形点重合
        if (!CollectionUtils.isEmpty(polygons)) {
            HashMap<String, List<BigDecimal>> qpolygonsHashMap = new HashMap<>();
            for (String polygon : polygons) {
                String s = preprocessParamName(polygon);
                List<BigDecimal> bigDecimals = map.get(s);
                qpolygonsHashMap.put(polygon, bigDecimals);
            }
            Map<String, List<BigDecimal>> stringListMap = filterDuplicatePoints(gkId, qpolygonsHashMap);
            if (CollectionUtils.isEmpty(stringListMap)) {
                log.info("多边形重合 {} {}", gkId, JSONObject.toJSONString(stringListMap));
                throw new BizException("多边形重合" + " " + gkId);
            }
        }

        // 圆上点重合(TODO特殊)
        if (!CollectionUtils.isEmpty(polygons)) {
            // 圆上点坐标几何
            HashMap<String, List<BigDecimal>> circlesHashMap = new HashMap<>();
            // 圆点坐标
            HashMap<String, List<BigDecimal>> OcirclesHashMap = new HashMap<>();

            for (String circle : circles) {
                if (circle.equals("O") || circle.equals("o")) {
                    // 获取远点坐标
                    List<BigDecimal> bigDecimals = map.get(circle);
                    OcirclesHashMap.put(circle, bigDecimals);
                    continue;
                }
                String s = preprocessParamName(circle);
                List<BigDecimal> bigDecimals = map.get(s);
                circlesHashMap.put(circle, bigDecimals);
            }
            for (String s : circlesHashMap.keySet()) {
                HashMap<String, List<BigDecimal>> map1 = new HashMap<>();
                map1.put(s, circlesHashMap.get(s));
                map1.put("O", OcirclesHashMap.get("O"));
                Map<String, List<BigDecimal>> stringListMap = filterDuplicatePoints(gkId, circlesHashMap);
                if (CollectionUtils.isEmpty(stringListMap)) {
                    log.info("圆点重合 {} {}", gkId, JSONObject.toJSONString(stringListMap));
                    throw new BizException("圆点重合" + " " + gkId);
                }
            }
        }

    }

    // 表示最小误差为1px
    private static final double DISTANCE_THRESHOLD = 5;

    /**
     * 表示传入点坐标判断点是否重合
     *
     * @param pointMap
     * @return
     */
    public static Map<String, List<BigDecimal>> filterDuplicatePoints(String gkId,
        Map<String, List<BigDecimal>> pointMap) {
        if (pointMap == null || pointMap.isEmpty()) {
            return new HashMap<>();
        }
        Map<String, List<BigDecimal>> resultMap = new LinkedHashMap<>();
        Map<String, List<BigDecimal>> processedPoints = new LinkedHashMap<>();

        for (Map.Entry<String, List<BigDecimal>> entry : pointMap.entrySet()) {
            String key = entry.getKey();
            List<BigDecimal> currentPoint = entry.getValue();
            isDuplicate(gkId, processedPoints, entry);
            resultMap.put(key, currentPoint);
            processedPoints.put(key, currentPoint);
        }
        return resultMap;
    }

    private static boolean isDuplicate(String gkId, Map<String, List<BigDecimal>> processedPoints,
        Map.Entry<String, List<BigDecimal>> entry) {

        for (String s : processedPoints.keySet()) {

            if (calculateDistance(entry.getValue(), processedPoints.get(s)) <= DISTANCE_THRESHOLD) {
                throw new BizException(gkId + " " + s + "点," + entry.getKey() + "点," + "坐标重合");
            }
        }
        return false;
    }

    private static double calculateDistance(List point1, List point2) {
        BigDecimal dx = new BigDecimal(point1.get(0).toString()).subtract(new BigDecimal(point2.get(0).toString()));
        BigDecimal dy = new BigDecimal(point1.get(1).toString()).subtract(new BigDecimal(point2.get(1).toString()));
        return Math.sqrt(dx.pow(2).add(dy.pow(2)).doubleValue());
    }

    private static String pointToString(List<BigDecimal> point) {
        return point.get(0).setScale(6, RoundingMode.HALF_UP) + "," + point.get(1).setScale(6, RoundingMode.HALF_UP);
    }

    private static List<BigDecimal> stringToPoint(String pointStr) {
        String[] parts = pointStr.split(",");
        return Arrays.asList(new BigDecimal(parts[0]), new BigDecimal(parts[1]));
    }

    /**
     * 判断几何对象中的点是否共线
     *
     * @param map          点坐标映射
     * @param stringSetMap 几何对象点集合映射
     */
    public static void checkPointsCollinear(String gkId, Map<String, List<BigDecimal>> map,
        Map<String, Set<String>> stringSetMap) {
        // 获取各种几何对象的点集
        Set<String> segments = stringSetMap.get("segment");
        Set<String> angles = stringSetMap.get("angle");
        Set<String> triangles = stringSetMap.get("triangle");
        Set<String> quadrilateras = stringSetMap.get("quadrilatera");
        Set<String> polygons = stringSetMap.get("polygon");
        Set<String> circles = stringSetMap.get("circle");
        Set<String> arcs = stringSetMap.get("circleArc");

        // 收集所有点的坐标
        Map<String, List<BigDecimal>> allPointsMap = new HashMap<>();

        // 处理各种几何对象的点
        collectPoints(map, segments, allPointsMap);
        collectPoints(map, angles, allPointsMap);
        collectPoints(map, triangles, allPointsMap);
        collectPoints(map, quadrilateras, allPointsMap);
        collectPoints(map, polygons, allPointsMap);
        collectPoints(map, circles, allPointsMap);
        collectPoints(map, arcs, allPointsMap);

        // 如果点数少于3，无法判断共线 只有线段场景不管
        if (allPointsMap.size() < 3 || (angles.isEmpty() && triangles.isEmpty() && quadrilateras.isEmpty()
            && polygons.isEmpty() && arcs.isEmpty() && circles.isEmpty())) {
            log.info("点数少于3 或没有几何图形，无法判断共线 {}", gkId);
            return;
        }

        // 检查所有点是否共线
        if (arePointsCollinear(allPointsMap)) {
            throw new BizException(gkId + " 所有点共线，不可绘制");
        }
    }

    /**
     * 收集点集合中的点坐标
     */
    private static void collectPoints(Map<String, List<BigDecimal>> map, Set<String> pointSet,
        Map<String, List<BigDecimal>> resultMap) {
        if (CollectionUtils.isEmpty(pointSet)) {
            return;
        }

        for (String point : pointSet) {
            String processedName = preprocessParamName(point);
            List<BigDecimal> coords = map.get(processedName);
            if (coords != null) {
                resultMap.put(point, coords);
            }
        }
    }

    /**
     * 判断点集是否共线
     */
    private static boolean arePointsCollinear(Map<String, List<BigDecimal>> pointsMap) {
        if (pointsMap.size() < 3) {
            return true; // 少于3个点总是共线的
        }

        // 取前两个点来确定一条直线
        List<List<BigDecimal>> points = new ArrayList<>(pointsMap.values());
        List<BigDecimal> p1 = points.get(0);
        List<BigDecimal> p2 = points.get(1);

        // 计算前两点的斜率

        BigDecimal x1 = new BigDecimal(String.valueOf(p1.get(0)));
        BigDecimal y1 = new BigDecimal(String.valueOf(p1.get(1)));
        BigDecimal x2 = new BigDecimal(String.valueOf(p2.get(0)));
        BigDecimal y2 = new BigDecimal(String.valueOf(p2.get(1)));

        // 检查剩余点是否在同一直线上
        for (int i = 2; i < points.size(); i++) {
            List<BigDecimal> p3 = points.get(i);
            BigDecimal x3 = p3.get(0);
            BigDecimal y3 = p3.get(1);

            // 使用行列式计算三点是否共线: (x2-x1)*(y3-y1) - (y2-y1)*(x3-x1) = 0
            BigDecimal det = (x2.subtract(x1)).multiply(y3.subtract(y1))
                .subtract((y2.subtract(y1)).multiply(x3.subtract(x1)));

            // 考虑浮点误差，使用阈值判断
            if (Math.abs(det.doubleValue()) > 1.0) {
                return false; // 点不共线
            }
        }

        return true; // 所有点共线
    }
}
