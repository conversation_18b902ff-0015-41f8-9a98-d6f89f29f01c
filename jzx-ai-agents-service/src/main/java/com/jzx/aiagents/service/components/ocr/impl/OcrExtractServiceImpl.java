package com.jzx.aiagents.service.components.ocr.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.jzx.aiagents.api.components.ocr.dto.OcrCutDTO;
import com.jzx.aiagents.api.components.ocr.dto.OcrExtractRequest;
import com.jzx.aiagents.api.components.ocr.vo.OcrExtractVO;
import com.jzx.aiagents.api.components.question.normalization.dto.ExtractCleanDTO;
import com.jzx.aiagents.api.components.question.normalization.dto.OCRInfoDto;
import com.jzx.aiagents.api.components.question.normalization.dto.OCRReqDto;
import com.jzx.aiagents.service.components.ocr.OCRFactory;
import com.jzx.aiagents.service.components.ocr.OcrExtractService;
import com.jzx.aiagents.service.components.ocr.processor.ImageProcessor;
import com.jzx.aiagents.service.utils.DataCleanUtil;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.HashMap;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 拍题图片OCR识别与提取配图解题服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
@RefreshScope
public class OcrExtractServiceImpl implements OcrExtractService {

    private static final String OCR_TYPE_DEFAULT = "mathpix";

    @Value("${jzx.components.cut-question.provider.jzx.base.url:https://jzx-ai-lab-base-test.91jzx.cn/visionmind/v1/layout}")
    private String jzxBaseCutQuestionUrl;

    @Value("#{${ocr.type:{}}}")
    private Map<String, String> ocrTypeMap;

    @Autowired
    private OCRFactory ocrFactory;

    @Autowired
    private DataCleanUtil dataCleanUtil;

    @Override
    public OcrExtractVO ocrCutExtract(OcrExtractRequest ocrExtractRequest) {
        String imgUrl = ocrExtractRequest.getImgUrl();
        String taskId = ocrExtractRequest.getTaskId();
        String subject = ocrExtractRequest.getSubject();
        long start = System.currentTimeMillis();
        Map<String, Object> requestParam = new HashMap<>();
        requestParam.put("img_url", imgUrl);
        requestParam.put("task_id", taskId);
        String requestBody = JSON.toJSONString(requestParam);
        log.info("精准学基础切题请求参数：{}", requestBody);
        // 创建HttpClient实例
        HttpClient client = HttpClient.newHttpClient();
        OcrExtractVO vo = new OcrExtractVO();
        try {
            // 构建POST请求
            HttpRequest request = HttpRequest.newBuilder()
                .uri(new URI(jzxBaseCutQuestionUrl))
                .header("Content-Type", "application/json")
                .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                .build();
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            String body = response.body();
            if (response.statusCode() != 200) {
                long end = System.currentTimeMillis();
                log.warn("精准学基础切题失败耗时:{}ms, statusCode:{}, errMsg:{}", end - start, response.statusCode(), body);
                throw new RuntimeException("精准学基础切题请求失败: " + body);
            }
            log.info("invoke base cut question method final result: {}", body);
            JSONObject jsonObject = JSON.parseObject(body);
            int code = jsonObject.getIntValue("code");
            String msg = jsonObject.getString("msg");
            if (code != 20000) {
                long end = System.currentTimeMillis();
                log.error("精准学基础切题失败耗时:{}ms, code:{}, errMsg:{}", end - start, code, msg == null ? "基础切题失败" : msg);
                throw new RuntimeException("精准学基础切题请求失败: code: " + code);
            }
            JSONObject data = jsonObject.getJSONObject("data");
            if (data == null) {
                throw new RuntimeException("精准学切题输出内容为空");
            }
            OcrCutDTO ocrCutDTO = ImageProcessor.processOcrImageData(imgUrl, data);
            vo.setFigureBase64List(ocrCutDTO.getFigureBase64List());
            vo.setTableBase64List(ocrCutDTO.getTableBase64List());
            if (BooleanUtils.isNotTrue(ocrExtractRequest.getIgnoreOcr())) {
                OCRReqDto ocrReqDto = new OCRReqDto();
                ocrReqDto.setOcrType(ocrTypeMap.getOrDefault(subject, OCR_TYPE_DEFAULT));
                ocrReqDto.setImgBytes(ocrCutDTO.getOriginalImage());
                OCRInfoDto ocrInfoDto = ocrFactory.getProcessor(ocrReqDto.getOcrType()).process(ocrReqDto);
                if (ocrInfoDto != null && StringUtils.hasText(ocrInfoDto.getContent())) {
                    // 再进行数据清洗
                    ExtractCleanDTO cleanDTO = dataCleanUtil.cleanContentBySubject(subject, ocrInfoDto.getContent());
                    if (cleanDTO != null && StringUtils.hasText(cleanDTO.getLatexText())) {
                        vo.setStem(cleanDTO.getLatexText());
                        log.info("OcrExtractServiceImpl ocrCutExtract  latexText :{}", cleanDTO.getLatexText());
                    }
                }
            }
        } catch (Exception e) {
            log.error("精准学基础切题请求异常：{}", e.getMessage());
            throw new RuntimeException(e);
        }
        return vo;
    }
}
   