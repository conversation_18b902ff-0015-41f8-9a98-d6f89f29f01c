package com.jzx.aiagents.service.components.ocr.processor;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.jzx.aiagents.api.components.ocr.dto.OcrCutDTO;
import java.awt.Color;
import java.awt.Graphics2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import com.jzx.aiagents.api.components.base.dto.PointDTO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import javax.imageio.ImageIO;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ImageProcessor {

    /**

public class ImageProcessor {

    /**
     * 从指定 URL 下载图片
     */
    public static BufferedImage downloadImage(String imageUrl) throws Exception {
        return ImageIO.read(new URL(imageUrl));
    }

    /**
     * 根据坐标裁剪图片
     *
     * @param originalImage 原始图片
     * @param x             裁剪起始X坐标
     * @param y             裁剪起始Y坐标
     * @param width         裁剪宽度
     * @param height        裁剪高度
     * @return 裁剪后的图片
     */
    public static BufferedImage cropImage(BufferedImage originalImage, int x, int y, int width, int height) {
        return originalImage.getSubimage(x, y, width, height);
    }

    /**
     * 在原始图片的指定区域画白色矩形进行覆盖（空白填充）
     *
     * @param image  原始图片
     * @param x      覆盖区域左上角X坐标
     * @param y      覆盖区域左上角Y坐标
     * @param width  覆盖区域宽度
     * @param height 覆盖区域高度
     */
    public static void fillAreaWithWhite(BufferedImage image, int x, int y, int width, int height) {
        Graphics2D g2d = image.createGraphics();
        g2d.setColor(Color.WHITE); // 设置为白色
        g2d.fillRect(x, y, width, height);
        g2d.dispose();
    }

    /**
     * 保存图片到本地
     */
    public static void saveImage(BufferedImage image, String outputPath, String format) throws Exception {
        File outputFile = new File(outputPath);
        ImageIO.write(image, format, outputFile);
    }

    public static OcrCutDTO processOcrImageData(String imgUrl, JSONObject data) throws Exception {
        // 1. 下载原始图片
        BufferedImage originalImage = downloadImage(imgUrl);

        // 2. 解析 figures_quad 和 tables_quad 坐标
        JSONArray figuresQuadArray = data.getJSONArray("figures_quad");
        JSONArray tablesQuadArray = data.getJSONArray("tables_quad");

        // 3. 裁剪 figures_quad 图片并转成 base64
        List<String> figureBase64List = new ArrayList<>();
        if (figuresQuadArray != null && !figuresQuadArray.isEmpty()) {
            for (Object quadObj : figuresQuadArray) {
                JSONArray quad = (JSONArray) quadObj;
                int x = quad.getJSONObject(0).getIntValue("x");
                int y = quad.getJSONObject(0).getIntValue("y");
                int width = quad.getJSONObject(2).getIntValue("x") - x;
                int height = quad.getJSONObject(2).getIntValue("y") - y;

                BufferedImage cropped = cropImage(originalImage, x, y, width, height);
                String base64 = convertToBase64(cropped, "png");
                figureBase64List.add(base64);

                // 同时填充该区域为白色
                fillAreaWithWhite(originalImage, x, y, width, height);
            }
        }

        // 4. 裁剪 tables_quad 图片并转成 base64
        List<String> tableBase64List = new ArrayList<>();
        if (tablesQuadArray != null && !tablesQuadArray.isEmpty()) {
            for (Object quadObj : tablesQuadArray) {
                JSONArray quad = (JSONArray) quadObj;
                int x = quad.getJSONObject(0).getIntValue("x");
                int y = quad.getJSONObject(0).getIntValue("y");
                int width = quad.getJSONObject(2).getIntValue("x") - x;
                int height = quad.getJSONObject(2).getIntValue("y") - y;

                BufferedImage cropped = cropImage(originalImage, x, y, width, height);
                String base64 = convertToBase64(cropped, "png");
                tableBase64List.add(base64);

                // 同时填充该区域为白色
                fillAreaWithWhite(originalImage, x, y, width, height);
            }
        }

        // 5. 处理 abandons_quad（仅填充）
        JSONArray abandonsQuadArray = data.getJSONArray("abandons_quad");
        if (abandonsQuadArray != null && !abandonsQuadArray.isEmpty()) {
            for (Object quadObj : abandonsQuadArray) {
                JSONArray quad = (JSONArray) quadObj;
                int x = quad.getJSONObject(0).getIntValue("x");
                int y = quad.getJSONObject(0).getIntValue("y");
                int width = quad.getJSONObject(2).getIntValue("x") - x;
                int height = quad.getJSONObject(2).getIntValue("y") - y;

                fillAreaWithWhite(originalImage, x, y, width, height);
            }
        }

        // 6. 保存处理后的图片（可选）
        // saveImage(originalImage, "processed_image.png", "png");

        // 7. 将裁剪出的图片 Base64 输出
        // System.out.println("figureBase64List: " + figureBase64List);
        // System.out.println("tableBase64List: " + tableBase64List);
        return new OcrCutDTO(toByteArray(originalImage), figureBase64List, tableBase64List);
    }

    /**
     * 将 BufferedImage 转换为 Base64 编码字符串
     */
    public static String convertToBase64(BufferedImage image, String format) throws Exception {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, format, baos);
        return Base64.getEncoder().encodeToString(baos.toByteArray());
    }

    public static List<List<Integer>> getImagePoint(List<List<PointDTO>> cnt) {
        List<List<Integer>> imageList = new ArrayList<>();

        // 如果cnt为空则不需要继续进去
        if (cnt == null || cnt.size() == 0) {
            return null;
        }
        for (List<PointDTO> points : cnt) {
            List<Integer> x = new ArrayList<>();
            List<Integer> y = new ArrayList<>();
            for (PointDTO point : points) {
                x.add(point.getX());
                y.add(point.getY());
            }
            // 取x中的最大值和最小值
            int maxx = x.get(0);
            int minx = x.get(0);
            for (int num : x) {
                if (num > maxx) {
                    maxx = num;
                }
                if (num < minx) {
                    minx = num;
                }
            }
            // 取y中的最大值和最小值
            int maxy = y.get(0);
            int miny = y.get(0);
            for (int num : y) {
                if (num > maxy) {
                    maxy = num;
                }
                if (num < miny) {
                    miny = num;
                }
            }
            List<Integer> imagePoint = new ArrayList<>();
            imagePoint.add(minx);
            imagePoint.add(miny);
            imagePoint.add(maxx - minx);
            imagePoint.add(maxy - miny);
            imageList.add(imagePoint);
        }
        return imageList;
    }

    public static List<String> processImageWithCropping(File tempFile, List<List<PointDTO>> cnt) {
        List<String> base64List = new ArrayList<>();
        try {
            if (tempFile == null || cnt == null || cnt.isEmpty()) {
                return base64List;
            }
            // 1. 加载原始图片
            BufferedImage originalImage = ImageIO.read(tempFile);

            // 2. 获取每个截图区域的坐标信息 [x, y, width, height]
            List<List<Integer>> imagePoints = getImagePoint(cnt);
            if (imagePoints == null || imagePoints.isEmpty()) {
                return List.of(); // 空列表返回
            }

            // 3. 遍历每个区域，进行裁剪并转换为 Base64
            for (List<Integer> point : imagePoints) {
                int x = point.get(0);
                int y = point.get(1);
                int width = point.get(2);
                int height = point.get(3);
                // 裁剪图片
                BufferedImage croppedImage = ImageProcessor.cropImage(originalImage, x, y, width, height);
                // 转换为 Base64 编码
                String base64Image = ImageProcessor.convertToBase64(croppedImage, "png");
                base64List.add(base64Image);
            }
        } catch (Exception e) {
            log.error("processImageWithCropping error", e);
        }
        return base64List;
    }

    /**
     * 将 BufferedImage 转换为 byte[]
     *
     * @param image BufferedImage 图像对象
     * @return byte[] 图像字节数据
     * @throws IOException 写入失败时抛出异常
     */
    public static byte[] toByteArray(BufferedImage image) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        boolean success = ImageIO.write(image, "png", baos);
        if (!success) {
            throw new IOException("不支持的图像格式: png");
        }
        return baos.toByteArray();
    }

    public static void main(String[] args) {
        try {
            String imageUrl = "https://zstt-dual-mentor.oss-cn-hangzhou.aliyuncs.com/question_alchemyenhanced/2675dcde-ca02-45db-bc73-f8e756f02353.jpg"; // 替换为你的图片URL
            String jsonData = "{\"img_base64\":null,\"boxes_quad\":[[{\"x\":68,\"y\":15},{\"x\":922,\"y\":15},{\"x\":922,\"y\":517},{\"x\":68,\"y\":517}]],\"boxes_yolo\":[[68,15,922,517]],\"answer_quad\":[],\"answer_yolo\":[],\"figures_quad\":[[{\"x\":521,\"y\":323},{\"x\":650,\"y\":323},{\"x\":650,\"y\":479},{\"x\":521,\"y\":479}],[{\"x\":372,\"y\":326},{\"x\":498,\"y\":326},{\"x\":498,\"y\":480},{\"x\":372,\"y\":480}]],\"figures_yolo\":[[521,323,650,479],[372,326,498,480]],\"tables_quad\":[],\"tables_yolo\":[],\"abandons_quad\":[],\"abandons_yolo\":[]}";
            processOcrImageData(imageUrl, JSONObject.parseObject(jsonData));

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

