package com.jzx.aiagents.service.components.solveqst.handler;

import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.jzx.aiagents.api.components.dify.request.DifyFileRequest;
import com.jzx.aiagents.api.components.question.normalization.dto.AnswerAndMethodResponse;
import com.jzx.aiagents.api.components.question.solution.req.QuestionTypeRequest;
import com.jzx.aiagents.api.components.question.solution.req.SolutionRequest;
import com.jzx.aiagents.api.model.enums.ApiKeySceneEnum;
import com.jzx.aiagents.api.model.enums.GradeEnum;
import com.jzx.aiagents.api.model.enums.QuestionRequestTypeEnum;
import com.jzx.aiagents.api.model.enums.QuestionTypeEnum;
import com.jzx.aiagents.api.model.enums.TtsSubjectEnums;
import com.jzx.aiagents.manager.enums.BizErrorCode;
import com.jzx.common.lang.exception.BizException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 计算题处理
 */
@Slf4j
@Component
public class CalculationQuestionHandler extends AbstractQuestionSolveHandler {

    @Override
    public String questionType(QuestionTypeRequest request) {
        if (StringUtils.isBlank(request.getQuestionImage())) {
            throw new BizException(BizErrorCode.STEM_BLANK_ERROR);
        }
        if (TtsSubjectEnums.MATH.code().equals(request.getSubject()) && GradeEnum.PRIMARY_GRADE_CODES.contains(
            request.getGrade())) {
            Boolean isCalculation = calculationJudgment(request);
            if (BooleanUtil.isTrue(isCalculation)) {
                return QuestionTypeEnum.CALCULATION_QUESTION.getType();
            }
        }
        return QuestionTypeEnum.OTHER_QUESTION.getType();
    }

    @Override
    public AnswerAndMethodResponse questionLLM(SolutionRequest param) {
        log.info("calculation questionLLM param: {}", JSON.toJSONString(param));
        Map<String, Object> inputs = new HashMap<>();
        if (StringUtils.isNotBlank(param.getQuestionStem())) {
            inputs.put("stem", param.getQuestionStem());
        }
        // 封装图片参数
        List<DifyFileRequest> files = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getQuestionImage())) {
            DifyFileRequest file = new DifyFileRequest();
            file.setType("image");
            file.setTransferMethod("remote_url");
            file.setUrl(param.getQuestionImage());
            files.add(file);
        }
        try {
            String answer = difyServiceHelper.getDifyResult(param, ApiKeySceneEnum.CALCULATION_PRIMARYMATH.getType(),
                inputs, files);
            // 如果转json失败，表示大模型返回非正常指定格式，则提示未找到题目信息
            answer = latexFixedService.fixedJson(answer);
            return JSON.parseObject(answer, AnswerAndMethodResponse.class);
        } catch (Exception e) {
            log.error("calculation questionLLM convert answer json error", e);
            throw new BizException(BizErrorCode.NOT_FOUND_STEM);
        }

    }

    @Override
    public String getRequestType() {
        return QuestionRequestTypeEnum.CALCULATION.getType();
    }

    @Override
    public String getQuestionType() {
        return QuestionTypeEnum.CALCULATION_QUESTION.getType();
    }

    private Boolean calculationJudgment(QuestionTypeRequest request) {
        log.info("calculationJudgment param: {}", JSON.toJSONString(request));
        List<DifyFileRequest> files = new ArrayList<>();
        DifyFileRequest file = new DifyFileRequest();
        file.setType("image");
        file.setTransferMethod("remote_url");
        file.setUrl(request.getQuestionImage());
        files.add(file);
        try {
            String answer = difyServiceHelper.getDifyResult(request, ApiKeySceneEnum.CALCULATION_JUDGMENT.getType(),
                Collections.EMPTY_MAP, files);
            // 如果转json失败，表示大模型返回非正常指定格式，则提示未找到题目信息
            answer = latexFixedService.fixedJson(answer);
            JSONObject jsonObject = JSON.parseObject(answer);
            Boolean judgment = jsonObject.getBoolean("judgment");
            log.info("calculationJudgment answer: {}", judgment);
            return judgment;
        } catch (Exception e) {
            log.error("calculationJudgment convert answer json error", e);
            throw new BizException(BizErrorCode.NOT_FOUND_STEM);
        }
    }

}
