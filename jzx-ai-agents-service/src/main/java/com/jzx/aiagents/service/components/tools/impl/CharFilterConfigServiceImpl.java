package com.jzx.aiagents.service.components.tools.impl;

import com.alibaba.fastjson2.JSON;
import com.jzx.aiagents.api.model.enums.CharFilterConfigEnum;
import com.jzx.aiagents.dao.api.CharFilterConfigDao;
import com.jzx.aiagents.dao.model.po.CharFilterConfigPO;
import com.jzx.aiagents.service.components.tools.CharFilterConfigService;
import com.jzx.aiagents.service.utils.StringReplacementUtil;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR> By AutoGenerator
 */
@Service
@Slf4j
public class CharFilterConfigServiceImpl implements CharFilterConfigService {

    @Autowired
    private CharFilterConfigDao charFilterConfigDao;

    /**
     * 主业务方法：过滤特殊字符 需要过滤的字符串小于50000
     */
    @Override
    public String charFilter(String businessType, String content) {
        long startTime = System.currentTimeMillis();
        if (!CharFilterConfigEnum.exists(businessType)) {
            log.warn("businessType is error,businessType:{}", businessType);
            return content;
        }
        if (StringUtils.isBlank(content) || content.length() > 5000) {
            log.warn("charFilter content is blank or too long: businessType:{},content:{}", businessType, content);
            return content;
        }
        List<CharFilterConfigPO> configPOList;
        LinkedHashMap<String, String> replaceMap;
        configPOList = charFilterConfigDao.getCharFilterConfigList(businessType);
        // 若 key 重复，使用后者
        replaceMap = configPOList.stream()
            .collect(Collectors.toMap(
                CharFilterConfigPO::getPatternChar,
                po -> StringUtils.defaultIfBlank(po.getReplaceChar(), ""),
                (existing, replacement) -> replacement,
                LinkedHashMap::new
            ));
        log.info("charFilter loading replaceMap:{}", JSON.toJSONString(replaceMap));
        if (CollectionUtils.isEmpty(replaceMap)) {
            return content;
        }
        // 直接替换匹配的字符
        String result = StringReplacementUtil.optimizedMatcherReplacement(content, replaceMap);
        long endTime = System.currentTimeMillis();
        long time = (endTime - startTime);
        if (time > 50) {
            log.warn("charFilter execution time:{}ms,businessType:{},content:{} ", time, businessType, content);
        }
        return result;
    }

}
