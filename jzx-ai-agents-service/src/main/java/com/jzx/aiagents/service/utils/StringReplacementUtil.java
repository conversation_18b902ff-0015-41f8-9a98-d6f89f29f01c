package com.jzx.aiagents.service.utils;


import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> lijq
 * @Description 字符串替换工具 ,对比了多种主流实现方案，后续如果有特殊场景可以先测试，后使用，默认使用原生实现
 * @menu
 * @Date 2024/12/19 18:15
 */
@Slf4j
public class StringReplacementUtil {

    /**
     * 选择题拼接选项前缀 输入
     */
    public static String generateOptions(List<String> options) {
        return Optional.ofNullable(options)
            .filter(o -> !o.isEmpty())
            .map(o -> IntStream.range(0, o.size())
                .mapToObj(i -> (char) ('A' + i) + ": " + o.get(i))
                .collect(Collectors.joining(", ")))
            .orElse("");
    }

    /**
     * 按照规则集 rules 依次对输入字符串 input 进行多次替换，返回最终替换后的字符串
     *
     * @param input
     * @param rules
     * @return
     */
    public static String optimizedMatcherReplacement(String input, LinkedHashMap<String, String> rules) {
        if (input == null || input.isEmpty() || input.length() > 5000 || rules == null || rules.isEmpty()) {
            log.warn("optimizedMatcherReplacementUsingReplaceAll Input is null/empty or rules are empty，input:{}", input);
            return input;
        }

        // 缓存编译后的 Pattern
        Map<Pattern, String> compiledRules = new LinkedHashMap<>();
        for (Map.Entry<String, String> entry : rules.entrySet()) {
            try {
                String replacement = entry.getValue() == null ? "" : entry.getValue();
                compiledRules.put(Pattern.compile(entry.getKey(), Pattern.DOTALL), replacement);
            } catch (PatternSyntaxException e) {
                log.error("optimizedMatcherReplacement Invalid regex in rules. Regex: {}, Error: {}", entry.getKey(), e.getMessage());
                // 如果有错误正则直接返回原始输入，避免影响后续
                return input;
            }
        }

        // 仅使用一个 StringBuilder 避免重复创建
        StringBuilder result = new StringBuilder(input);
        for (Map.Entry<Pattern, String> entry : compiledRules.entrySet()) {
            Matcher matcher = entry.getKey().matcher(result);
            StringBuilder buffer = new StringBuilder();
            while (matcher.find()) {
                matcher.appendReplacement(buffer, entry.getValue());
            }
            matcher.appendTail(buffer);
            // 替换结果直接更新到 result
            result.setLength(0);
            result.append(buffer);
        }
        return result.toString();
    }

    public static void testmatcherReplacementUsingReplaceAll(String input, LinkedHashMap<String, String> rules) {
        long startTime = System.nanoTime();
        String result = optimizedMatcherReplacement(input, rules);
        long endTime = System.nanoTime();
        System.out.println("Execution time: " + (endTime - startTime) / 1000 + " us");
        System.out.println(result);
    }


    public static void main(String[] args) {
        List<String> answers = new ArrayList<String>();
        String str = generateOptions(answers);
        System.out.println(str);
//        String text = "在一元二次方程 \\\\ax^2+bx+c=0\\\\ 中，a≠0是必要条件。这条件非常重要，因为只有a不等于0，整个函数才是一元二次方程。所以题目中给的是 a≠0 哦。你觉得为什么a不能等于0呢？";
//        System.out.println(text);
//        LinkedHashMap<String, String> rules = new LinkedHashMap<>();
//        rules.put("(\\$\\$(?:[^\\$]|(?<!\\$)\\$)*?)%(.*?\\$\\$)", "$1\\\\%$2");
//        rules.put("\\$\\$(.*?)\\$\\$", "⢪$1⢫");
//        rules.put("\\$\\$(.*?)\\\\\\)", "⢪$1⢫");
//        rules.put("\\\\\\((.*?)\\$\\$", "⢪$1⢫");
//        rules.put("\\\\\\((.*?)\\\\\\)", "⢪$1⢫");
//        rules.put("\\(\\$(.*?)\\$\\)", "⢪$1⢫");
//        rules.put("\\$(.*?)\\$", "⢪$1⢫");
//        rules.put("1", "one");
//        rules.put("2", null);
//        rules.put("😃", "");
//        rules.put("™️", "");
//        testmatcherReplacementUsingReplaceAll(text, rules);
    }
}