package com.jzx.aiagents.service.utils;

import java.io.File;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import lombok.extern.slf4j.Slf4j;
import net.jpountz.xxhash.XXHashFactory;

/**
 * 类功能描述
 *
 * <AUTHOR>
 * @date 2024/6/8 17:10:36
 */
@Slf4j
public class XxHashUtil {

    public static long getXxHash32ByFile(File file, int send) throws IOException {
        int hash = XXHashFactory.fastestInstance().hash32()
            .hash(ByteBuffer.wrap(Files.readAllBytes(file.toPath())), send);
        return Integer.toUnsignedLong(hash);
    }

    public static long getXxHash32ByFile(File file) throws IOException {
        return getXxHash32ByFile(file, 6201);
    }

    public static long getXxHash32ByContentAndPath(String str) throws IOException {
        int hash = XXHashFactory.fastestInstance().hash32()
            .hash(ByteBuffer.wrap(str.getBytes(StandardCharsets.UTF_8)), 6201);
        return Integer.toUnsignedLong(hash);
    }

    public static void main(String[] args) {
        try {
            int xxhash = -608755480;
            long unsignedHash = xxhash & 0xFFFFFFFFL;
            long unsignedLong = Integer.toUnsignedLong(xxhash);
            long str1 = getXxHash32ByContentAndPath("" + "今天天气很好");
            long str2 = getXxHash32ByContentAndPath("今天天气很好");
            long str = getXxHash32ByContentAndPath("啊好像有人进来了，你好呀~");
            long str3 = getXxHash32ByContentAndPath(
                "你今天的表现为自己赢得了一枚闪亮的“数学之星”勋章。这枚勋章代表着你的智慧、勇气和创造力。");
            System.out.println(true);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }
}