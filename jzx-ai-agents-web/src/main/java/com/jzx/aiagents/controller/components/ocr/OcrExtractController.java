package com.jzx.aiagents.controller.components.ocr;

import com.jzx.aiagents.api.components.ocr.dto.OcrExtractRequest;
import com.jzx.aiagents.api.components.ocr.vo.OcrExtractVO;
import com.jzx.aiagents.service.components.ocr.OcrExtractService;
import com.jzx.common.lang.annotation.AutoLog;
import com.jzx.common.lang.model.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * ocr的controller
 */
@RestController
@RequestMapping("/aa/components/ocr")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class OcrExtractController {

    private final OcrExtractService ocrExtractService;

    /**
     * 拍题图片OCR识别与提取配图
     *
     * @param ocrExtractRequest
     * @return
     */
    @PostMapping("/cut/extract")
    public Result<OcrExtractVO> ocrExtract(@RequestBody OcrExtractRequest ocrExtractRequest) {
        return Result.success(ocrExtractService.ocrCutExtract(ocrExtractRequest));
    }

}
   
