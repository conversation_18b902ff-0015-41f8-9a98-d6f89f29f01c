package com.jzx.aiagents.controller.components.tool;


import com.jzx.aiagents.api.components.tool.request.CharFilterReqVO;
import com.jzx.aiagents.service.components.tools.CharFilterConfigService;
import com.jzx.common.lang.model.Result;
import jakarta.validation.Valid;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> lijq
 * @Description 拍搜相似题服务
 * @menu
 * @Date 2024/11/25 21:24
 */
@Slf4j
@RestController
@RequestMapping("/aa/charFilterConfig")
@Validated
public class CharFilterConfigController {

    @Autowired
    private CharFilterConfigService charFilterConfigService;

    /**
     * 根据不同的配置规则过滤字符串
     *
     * @param req content      长度不超过5000
     * @return
     */
    @PostMapping("/charFilter")
    public Result<String> charFilter(@Valid @RequestBody CharFilterReqVO req) {
        String result = charFilterConfigService.charFilter(req.getBusinessType(), req.getContent());
        return Result.success(result);
    }


}